/*
 * @Author: xu.sun <EMAIL>
 * @Date: 2022-07-26 18:41:00
 * @LastEditors: xu.sun <EMAIL>
 * @LastEditTime: 2023-02-10 18:30:37
 * @FilePath: /itp-operation-web/src/api/position.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useUserStore } from "@/store/user.store";
import request from "@/utils/request";

export enum QUERY_ACTION {
  EQ = "eq",
  GT = "gt",
  LT = "lt",
  LIKE = "like",
  IN = "in",
}

export interface Query {
  // 操作
  action: QUERY_ACTION;

  // 操作的对象
  key: string;

  // 操作的值
  value: number[] | number;
}

export interface PositionQueryParams {
  // 职位搜索关键字
  name?: string;
  // 查询
  queries?: Query[];
  // 简历状态
  status?: POSITION_STATUS;

  size: number;
  current: number;
}

export enum POSITION_STATUS {
  ONGOING = 1,
  SUSPEND = 2,
  END = 3,
  STOPPED = 4,
}

// 获取一个企业的流程表头
export function getJobRequireHeaderIndex() {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/pipeline/job/require/header/index`,
    method: "get",
  });
}

// 查询职位请求
export function getJobRequireSearch(
  query: PositionQueryParams = {
    size: 20,
    current: 1,
  }
) {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/pipeline/job/require/search`,
    method: "post",
    data: query,
  });
}

// 查询职位请求
export function getMyJobRequireSearch(
  query: PositionQueryParams = {
    size: 20,
    current: 1,
  }
) {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/pipeline/job/require/search/by/me`,
    method: "post",
    data: query,
  });
}

export function getJobTaskFormDefinition(
  processDefinitionId: number,
  taskDefinitionKey: string
) {
  const userStore = useUserStore();
  return request({
    url: `/company/${userStore.companyId}/pipeline/define/${processDefinitionId}/${taskDefinitionKey}`,
    method: "GET",
  });
}

interface finishJobTaskParams {
  isSuccessComplete: boolean;
  variables: any;
}
export function finishJobTask(
  jobRequirementId: number,
  taskId: string,
  params: finishJobTaskParams
) {
  const userStore = useUserStore();
  return request.post(
    `company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/task/${taskId}`,
    params
  );
}

export function getJobMatchList(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool?size=20`
  );
}

export function changeJobMathGroup(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool`
  );
}

export function passJobMatchItem(jobRequirementId: number, poolId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool/${poolId}/pass`
  );
}

export function passJobMatchTalentItem(
  jobRequirementId: number,
  talentId: number
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/talent/${talentId}/pass`
  );
}

export function ignoreJobMatchItem(jobRequirementId: number, poolId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool/${poolId}/ignore`
  );
}

export function ignoreJobMatchGroup(
  jobRequirementId: number,
  poolIds: number[]
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool/ignore`,
    {
      poolIds: poolIds,
    }
  );
}

export function rejectJobMatchItem(jobRequirementId: number, poolId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/pool/${poolId}/reject`
  );
}

export function rejectJobMatchTalentItem(
  jobRequirementId: number,
  talentId: number
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/talent/${talentId}/reject`
  );
}

export function getJobRequimentPoolCount(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/talent/pool/count`
  );
}

interface createSubProcessByTaskProps {
  taskId: string;
  jobRequirementId: number;
  processDefinitionKey: string;
  completeParentTask: boolean;
  passIfComplete?: boolean;
  updateParentTaskVariables?: any;
  variables?: any;
  updateNextTaskVariables?: any;
  assigneeId: number;
  parentNextTaskDefinitionKey?: string;
  completeParentTaskVariables?: any;
  updateParentTaskNextLocalVariables?: any;
  updateParentTaskLocalVariables?: any;
}

export function createSubProcessByTask(props: createSubProcessByTaskProps) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${props.jobRequirementId}/task/${props.taskId}/subprocess/${props.processDefinitionKey}`,
    {
      completeParentTask: props.completeParentTask,
      passIfComplete: props.passIfComplete,
      assigneeId: props.assigneeId,
      variables: props.variables,
      updateNextTaskVariables: props.updateNextTaskVariables,
      parentNextTaskDefinitionKey: props.parentNextTaskDefinitionKey,
      updateParentTaskVariables: props.updateParentTaskVariables,
      completeParentTaskVariables: props.completeParentTaskVariables,
      updateParentTaskNextLocalVariables:
        props.updateParentTaskNextLocalVariables,
      updateParentTaskLocalVariables: props.updateParentTaskLocalVariables,
    }
  );
}

export function getCustomerJobRequirements(customerId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/by/customer/${customerId}`
  );
}

interface GetJobRequirementTaskListParams {
  assignee?: number;
  jobRequirementId: number;
  taskDefinitionKey: string;
  current: number;
  size: number;
  status?: number;
}

export function getJobRequirementTaskList(
  params: GetJobRequirementTaskListParams
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${params.jobRequirementId}/task/define/${params.taskDefinitionKey}`,
    {
      params: {
        current: params.current,
        size: params.size,
        assignee: params.assignee,
        status: params.status,
      },
    }
  );
}

export function getJobRequirementTask(
  jobRequirementId: number,
  taskId: string
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/task/${taskId}`
  );
}

interface GetJobRequirementFinishedTaskListParams {
  jobRequirementId: number;
  processDefinitionKey: string;
  current: number;
  size: number;
}

export function getJobRequirementFinishedTaskList(
  params: GetJobRequirementFinishedTaskListParams
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${params.jobRequirementId}/processes/${params.processDefinitionKey}/finished`,
    {
      params: { current: params.current, size: params.size },
    }
  );
}

interface PaginationParam {
  current: number;
  size: number;
}

interface getFollowUpParams extends PaginationParam {
  assigneeId?: number;
}

export function getJobRequirementFollowUp(
  jobRequirementId: number,
  params: getFollowUpParams
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/followups`,
    { params }
  );
}

interface CreateFollowUpParams {
  comment: string;
}

export function createJobRequirementFollowUp(
  jobRequirementId: number,
  params: CreateFollowUpParams
) {
  const userStore = useUserStore();
  return request.post(
    `company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/followup`,
    params
  );
}

export function addTalentToJobPool(jobRequirementId: number, talentId: number) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/addToJobRequirement`,
    {
      jobRequirementId,
      talentId,
    }
  );
}

interface updateJobRequirementParams {
  status: number
  priority: number
  canPublishToPlatform: number
  invertedIndex: { key: string; value: any }[]
}

export function updateJobRequirement(
  jobRequirementId: number,
  params: updateJobRequirementParams
) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}`,
    params
  );
}

interface interviewTaskActionParams {
  parentTaskId?: string;
  completeParentTask?: boolean;
  isParentSuccessComplete?: boolean;
  parentTaskVariables?: {
    [key: string]: any;
  };
  talentId?: number;
  variables?: {
    [key: string]: any;
  };
}

export function addInterviewTask(
  jobRequirementId: number,
  params: interviewTaskActionParams
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/interview`,
    params
  );
}

export function getInterviewTaskList(
  jobRequirementId: number,
  talentId: number
) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/talent/${talentId}/interviews/`
  );
}

export function updateInterviewTaskVariables(
  jobRequirementId: number,
  taskId: string,
  params: interviewTaskActionParams
) {
  const userStore = useUserStore();
  return request.put(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/interview/${taskId}`,
    params
  );
}

export function updateTaskVariables(
  jobRequirementId: number,
  taskId: string,
  params: any
) {
  const userStore = useUserStore();
  return request.post(
    `/company/${userStore.companyId}/pipeline/job/require/${jobRequirementId}/variables/task/${taskId}`,
    params
  );
}

export function getTalentJob(talentId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/job/require/pipeline/by/talent/${talentId}`
  );
}

// 通过 JobRequirement 获取已Offer流程
export function getProcessInstances(jobRequirementId: number) {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/jobRequirement/${jobRequirementId}/processInstances`
  );
}

// 获取项目流程列表
export function getTaskDefinitionKeys() {
  const userStore = useUserStore();
  return request.get(
    `/company/${userStore.companyId}/pipeline/process/pm/taskDefinitionKeys`
  );
}

type ShareJobParams = {
  companyIds: number[]
  allowPublicControl: number
  coveredCustomerDesc: string
  coveredCustomerName: string
  coveredPositionName: string
  coveredPositionDesc: string
  jobRequirementId: number
  // positionId: number
}

export function shareJobToOtherCompany(params: ShareJobParams) {
  const userStore = useUserStore()
  return request.post(`/company/${userStore.companyId}/platform/publish`, params)
}

export function getJobShareInfo(jobId:number) {
  const userStore = useUserStore()
  return request.get(`/company/${userStore.companyId}/platform/jobRequirement/${jobId}`)
}

export function getJobRequirementInterviews(companyId: any, jobRequirementId: any) {
  return request.get(`/company/${companyId}/pipeline/job/require/${jobRequirementId}/interviews`)
}

export function searchJobRequirementTasks(companyId: number, jobRequirementId: number, searchParams: any, current: number, size: number) {
  return request.post(
    `/company/${companyId}/pipeline/job/require/${jobRequirementId}/search/tasks?current=${current}&size=${size}`,
    searchParams
  )
}