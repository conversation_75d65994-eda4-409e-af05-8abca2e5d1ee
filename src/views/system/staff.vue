<template lang="pug">

mixin page-header
  a-page-header(
    title="员工管理",
    style="padding: 0 0 8px"
    @back="()=>{$router.go(-1)}"
  )
    template(#extra)
      a-button(type="primary", @click="handleNewStaff") 添加员工
        template(#icon)
          UserAddOutlined

mixin filter-section
  section.staff-filter
    a-row.search-filter(:gutter="12")
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="状态")
          a-select(
            allow-clear,
            :filter-option="false"
            :dropdownMatchSelectWidth="false"
            @change="handleStatusChange"
            v-model:value="filterValues.status"
          )
            a-select-option(value="ONTHEJOB") 在职
            a-select-option(value="RESIGNED") 已离职

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="类型")
          a-select(
            allow-clear,
            :filter-option="false"
            :dropdownMatchSelectWidth="false"
            @change="handleTypeChange"
            v-model:value="filterValues.type"
          )
            a-select-option(value="全职") 全职
            a-select-option(value="兼职") 兼职

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        FilterItem(label="姓名")
          a-input(
            allow-clear,
            v-model:value="filterValues.name"
            placeholder="请输入姓名"
            @change="handleNameChange"
          )

mixin staff-list
  .staff-list
    a-spin(:spinning="status.staffListLoading")
      a-table(
        :columns="tableColumnConfig",
        :dataSource="displayStaffList",
        :pagination="null"
        :customRow="handleCustomRow"
        rowClassName="clickable"
      )
        template(#bodyCell="{ column, record }")
          //- 头像部分
          template(v-if="column.dataIndex === 'name'")
            span {{ record.realName }}

          template(v-if="column.key === 'avatar'")
            a-avatar(:src="record.formalPhoto") {{ record.realName[0] }}

          template(v-if="column.key == 'type'")
            a-tag(v-if="record.isAdministrator" color="#FF9111") 管理员
            a-tag(v-if="record.employeeTypeStr == '兼职'" color="#2db7f5") {{ record.employeeTypeStr }}
            a-tag(v-if="record.status==0" color="#2665FC") 已离职

          //- 角色
          template(v-if="column.dataIndex === 'roles'") 
            span(v-if="record.roles.length > 0") {{ (record.roles.map((item: any) => item.roleName)).join(', ') }}
            span(v-else) 无角色

          //- 部门
          template(v-if="column.dataIndex === 'department'") 
            span {{ record.departments.length > 0 ? record.departments[0].name : '' }}

          //- 性别
          template(v-if="column.dataIndex === 'gender'") 
            span(v-if="record.gender === 1") 男
            span(v-else-if="record.gender === 2") 女
            span(v-else-if="record.gender === 3") 其他
            span(v-else) 未知

          //- 操作
          template(v-if="column.key === 'operation'")
            a-space(:size="8")
              a-button 编辑
              a-button(type="danger" v-if="record.status===1" @click.stop="handleDepartBtnClick(record.id)") 操作离职
              a-button(v-else @click.stop="handleReOnboardBtnClick(record.id)") 重新入职

.staff-list-page
  +page-header
  main
    +filter-section
    +staff-list

  a-drawer(
    v-model:open="drawer.show",
    placement="right",
    :title="drawer.title",
    :destroyOnClose="true",
    :width="640"
    :bodyStyle="{padding: 0}"
  )
    component(
      :is="drawer.component",
      v-if="drawer.component",
      v-bind="drawer.props",
      @close="drawer.show = false",
      @update:success="handleStaffAddSuccess"
    )
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowReactive } from 'vue'
import { UserAddOutlined } from '@ant-design/icons-vue'
import StaffEdit from '@/components/app/staff-edit.vue'
import { getCompanyUserList, departCompanyUser, reOnboardCompanyUser, getUserScoreFieldTypesByRole } from '@/api/system/users'
import { message, Modal } from 'ant-design-vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import FilterItem from '@/components/ui/filter-item.vue'

const router = useRouter()
const staffSearchParams = reactive({
  role: [],
  name: '',
})

const pagination = reactive({
  current: 0,
  pagesize: 20,
  total: 100,
})

const drawer = shallowReactive({
  title: '',
  show: false,
  component: null as null | any,
  props: {} as any
})

const status = reactive({
  staffListLoading: false
})

const filterValues = reactive({
  status: 'ONTHEJOB',
  type: null as string | null,
  name: '' as string
})

const userScoreTypes = reactive<any>({
  ca: [],
  pm: []
})

// 在不需要响应式的情况下，可以直接返回固定的值
const tableColumnConfig = [
  { title: 'ID', key: 'id', dataIndex: 'id' },
  { title: '员工', key: 'avatar', width: 60 },
  { title: '', key: 'name', dataIndex: 'name' },
  { title: '性别', key: 'gender', dataIndex: 'gender' },
  { title: '类型', key: 'type' },
  { title: '部门', key: 'department', dataIndex: 'department' },
  { title: '角色', key: 'roles', dataIndex: 'roles' },
  { title: '操作', key: 'operation', width: 220 },
]

let staffList: any[] = []
const displayStaffList = ref<any[]>([])

async function getUserScoreFieldTypes() {
  try {
    const caRes = await getUserScoreFieldTypesByRole('ca')
    const pmRes = await getUserScoreFieldTypesByRole('pm')
    userScoreTypes.ca = caRes.data
    userScoreTypes.pm = pmRes.data
  } catch (err: any) {
    console.log(err.message)
    message.error(err.message)
  }

}

async function getStaffList() {
  status.staffListLoading = true
  try {
    const res = await getCompanyUserList()
    staffList = res.data
    filterStaff(filterValues)
  } catch (err) {
    message.error('员工信息加载失败，请重试')
  }
  status.staffListLoading = false
}

function handleUpdateStaff(staff: any) {
  drawer.title = '修改'
  drawer.show = true
  drawer.component = StaffEdit
  drawer.props = { staff, userScoreTypes }
}

function handleNewStaff() {
  drawer.title = '添加员工'
  drawer.show = true
  drawer.component = StaffEdit
  drawer.props = { userScoreTypes }
}

function handleStaffAddSuccess() {
  getStaffList()
  drawer.show = false
}

async function departStaff(companyUserId:number) {
  try {
    departCompanyUser(companyUserId)
    const staff = displayStaffList.value.find((item) => item.id === companyUserId)
    staff.status = 0
  } catch (err: any) {
    message.error(err.message)
  }
}

async function reOnboardStaff(companyUserId:number) {
  try {
    const res = reOnboardCompanyUser(companyUserId)
    const staff = displayStaffList.value.find((item) => item.id === companyUserId)
    staff.status = 1
  } catch (err: any) {
    message.error(err.message)
  }
}

function handleDepartBtnClick(companyUserId: number) {
  Modal.confirm({
    title: "确定将该员工操作离职？",
    content: "该操作不可逆，离职后的员工无法恢复，请谨慎操作！（该员工产生的历史数据和日志仍将保留。）",
    onOk: async() => departStaff(companyUserId)
  })
}

function handleReOnboardBtnClick(companyUserId: number) {
  Modal.confirm({
    title: "确定将该员工重新入职？",
    content: "该操作将会使用该员工历史数据，请注意修改。",
    onOk: async () => reOnboardStaff(companyUserId)
  })
}

function handleStatusChange(status: string) {
  filterValues.status = status
  filterStaff(filterValues)
}

function handleTypeChange(type: string | null) {
  filterValues.type = type
  filterStaff(filterValues)
}

function handleNameChange() {
  filterStaff(filterValues)
}

function filterStaff(filter: any) {
  let list = staffList
  if (filter.status == 'ONTHEJOB') {
    list = list.filter((item) => !item.departDate)
  }
  if (filter.status == 'RESIGNED') {
    list = list.filter(item => item.departDate)
  }
  if (filter.type != null) {
    list = list.filter(item => item.employeeTypeStr === filter.type)
  }
  if (filter.name) {
    const searchTerm = filter.name.toLowerCase()
    list = list.filter(item => item.realName.toLowerCase().includes(searchTerm))
  }

  displayStaffList.value = list
}

onBeforeRouteLeave(() => {
  drawer.show = false
})

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      handleUpdateStaff(record)
      filterStaff(filterValues)
    }
  }
}

onMounted(async () => {
  await getUserScoreFieldTypes()
  await getStaffList()
})

</script>

<style lang="scss" scoped>

main {
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
}

.staff-list-page {
  padding-bottom: 24px;

}

section.staff-filter {
  background-color: #fff;
  padding: 20px 16px;
}

.staff-list {
  border-radius: 8px;
}

.staff-item {
  &__name {
    padding: 0 12px;
    display: inline;
  }

  &__tag {
    margin-left: 8px;
  }
}
</style>