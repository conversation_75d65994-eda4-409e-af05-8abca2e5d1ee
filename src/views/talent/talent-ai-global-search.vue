<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { SearchOutlined, LoadingOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue'
import { WORK_YEAR } from '@/consts/multi'
import { getMultiSearchTalentList, getMultiSearchTalentParams, saveSmartDeerAppTalent, unionSearchTranslateParams, unionSearchTalentDetail } from '@/api/talent/talent'
import { dictionary, getAllIndustryList, getDegreeList } from "@/api/dictionary"
import { message } from 'ant-design-vue'
import { setSearchBury } from "@/api/track"
import { getPushSetting } from '@/api/ai'

import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import TalentSearchItem from '@/components/app/talent-search-item.vue'
import ChatBox from '@/components/chat/chat-box.vue'

import LiepinService from '@/service/multi/liepin/index-v2'
import MaimaiService from '@/service/multi/maimai/index-v2'
import SmartDeerService from '@/service/multi/smartdeer/index-v2'
import BossService from '@/service/multi/boss/index-v2'
import LinkedInService from '@/service/multi/linkedin/index-v2'
import ItpTalentService from '@/service/multi/itp-talent/index-v2'
import { areaDictToTreeData } from '@/utils/form-data-helper'
import { watch } from 'vue'
import tracker from '@/utils/tracker'
import GlobalTalentService from '@/service/multi/global-talent/index-v2'
import TalentGlobalSearchItem from '@/components/app/talent-global-search-item.vue'
import { unionSearchParamsCompany, unionSearchParamsMajor, unionSearchParamsSchool, unionSearchParamsArea } from '@/api/talent/talent'
import TalentGlobalDetailModal from '@/components/app/talent-global-detail-modal.vue'
import AIChatBox from '@/components/ai/ai-chat-box.vue'

const env = import.meta.env.VITE_VUE_APP_BUILD_ENV

const liepin = new LiepinService()
const maimai = new MaimaiService()
const smartdeer = new SmartDeerService()
const boss = new BossService()
const linkedin = new LinkedInService()
const itpTalent = new ItpTalentService()
const globalTalent = new GlobalTalentService()

const talentId = ref<number>()
const trackId = ref('')
const aiSettings = ref<any>({})

const globalTalentId = ref<number>()
const talentInfo = ref<any[]>([])

// jobId是当全网搜针对某个职位进行搜索时，需要的参数。
const props = defineProps<{ jobId?: number }>()

const status = reactive({
  spinning: false,
  showTalentDetail: false,
  showGlobalTalentDetail: false,
  showChatBox: false,
  initDict: false,
  showAIChatBox: false
})

const activeTab = ref('globalTalent')
const totalTalentList = ref([] as any[])

const dict = reactive({
  degree: [] as any[],
  degreeMap: [] as any[],
  industry: [] as any[],
  city: [] as any[],
})
// 这两个map用于转换ITP参数的时候，将IT转成字符串使用
let IndustryMap = new Map()
let AreaMap = new Map()

// 初始化字典
async function initDict() {
  status.initDict = true
  try {
    const [dictArea, dictIndustry] = await Promise.all([
      unionSearchParamsArea(), getAllIndustryList()
    ])

    const dictAreaTreeData = [] as any[]
    const areaList = dictArea.data.list
    areaList.forEach((item: any, index: number) => {
      const childAreaList = item.child
      const dictChildAreaTreeData = [] as any[]
      for (let i = 0; i < childAreaList.length; i++) {
        const child = childAreaList[i]
        dictChildAreaTreeData.push({
          title: child.name,
          id: child.id,
          children: []
        })
        AreaMap.set(child.id, child)
      }
      const tmpObject = {
        title: item.name,
        id: item.id,
        children: dictChildAreaTreeData
      }
      dictAreaTreeData.push(tmpObject)
      AreaMap.set(item.id, item)
    })
    // const [dictAreaTreeData, dictAreaMap] = areaDictToTreeData(dictArea.data)
    dict.city = dictAreaTreeData as any[]
    // AreaMap = dictAreaMap as Map<number, any>

    const dictIndustryMap = new Map()
    const tempIdustryList = [] as any[]
    dictIndustry.data.forEach((item: any, index: number) => {
      const targetObj = Object.assign({}, item, { label: item.industryName, value: item.id, children: [] })
      dictIndustryMap.set(item.id, targetObj)
      if (item.parentId === 0) tempIdustryList.push(targetObj)
    })

    dictIndustryMap.forEach((item, key) => {
      if (item.parentId === 0) return
      const parent = dictIndustryMap.get(item.parentId)
      parent.children.push(item)
    })

    dict.industry = tempIdustryList
    IndustryMap = dictIndustryMap

    // 初始化教育经历字典
    const res = await getDegreeList()
    dict.degreeMap = res.data
    dict.degree = Object.keys(res.data).map((value: any) => {
      return { label: res.data[value], value: parseInt(value) }
    })
  } catch (err: any) {
    message.error('抱歉，初始化字典失败, 请重试。')
  }
  status.initDict = false
}

async function handleTotalTalentNextPage() {
  status.spinning = true

  try {
    const requestList = [
      liepin.nextPage(),
      maimai.nextPage(),
      boss.nextPage(),
      smartdeer.nextPage(),
      linkedin.nextPage(),
    ]
    const [lieDataDto, maiDataDto, bossDataDto, smartdeerDataDto, lingDataDto] = await Promise.all(requestList)

    const listQueryParams = {
      lieDataDto, maiDataDto, smartdeerDataDto, bossDataDto, lingDataDto,
      queryDto: []
    }

    const aggregateListData = await getMultiSearchTalentList(listQueryParams)
    lastSearchRank = aggregateListData.data.maxItpTalentRank
    totalTalentList.value = aggregateListData.data.data
    await updateTalentInfoForScore()
  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

const updateTalentInfoForScore = async () => {
  // Merge talent data from all sources in specified order
  talentInfo.value = [
    ...maimai.list.map(item => ({ ...item, site: 3 })),  // maimai
    ...liepin.list.map(item => ({ ...item, site: 2 })),  // liepin
    ...linkedin.list.map(item => ({ ...item, site: 4 })), // linkedin
    ...boss.list.map(item => ({ ...item, site: 5 })),    // boss
    ...smartdeer.list.map(item => ({ ...item, site: 6 })), // smartdeer
    ...globalTalent.list.map(item => ({ ...item, site: 7 })) // itp
  ]
}

const updateGlobalTalentScore = async (site: any, talentId: number, resumeId: any, score: any) => {
  if (site === 7) {
    for (let i = 0; i < globalTalent.list.length; i++) {
      const item = globalTalent.list[i]
      if (item.id === talentId) {
        item.score = score
        break
      }
    }
  } else if (site === 2) {
    for (let i = 0; i < liepin.list.length; i++) {
      const item = liepin.list[i]
      if (item.resumeId === resumeId) {
        item.score = score
        break
      }
    }
  } else if (site === 3) {
    for (let i = 0; i < maimai.list.length; i++) {
      const item = maimai.list[i]
      if (item.resumeId === resumeId) {
        item.score = score
        break
      }
    }
  } else if (site === 4) {
    for (let i = 0; i < linkedin.list.length; i++) {
      const item = linkedin.list[i]
      if (item.resumeId === resumeId) {
        item.score = score
        break
      }
    }
  } else if (site === 5) {
    for (let i = 0; i < boss.list.length; i++) {
      const item = boss.list[i]
      if (item.resumeId === resumeId) {
        item.score = score
        break
      }
    }
  }
}

// 这个变量用于记录"全部聚合" TAB的数据转换返回的最后的页码。
let lastSearchRank = 0

async function handleSearch(searchParams: any) {
  status.spinning = true
  try {

    const smartdeerParamsJson = JSON.parse(searchParams['smartdeer'])
    const liepinParamsJson = JSON.parse(searchParams['liepin'])
    const maimaiParamsJson = JSON.parse(searchParams['maimai'])
    const linkedinParamsJson = JSON.parse(searchParams['linkedin'])
    const bossParamsJson = JSON.parse(searchParams['boss'])
    const globalTalentParamsJson = JSON.parse(searchParams["itp"])

    smartdeerParamsJson.search_id = new Date().getTime() + ''

    const requestList = [
      liepin.fetch(liepinParamsJson, smartdeerParamsJson),
      maimai.fetch(maimaiParamsJson, smartdeerParamsJson),
      boss.fetch(bossParamsJson, smartdeerParamsJson),
      smartdeer.fetch(smartdeerParamsJson),
      linkedin.fetch(linkedinParamsJson, smartdeerParamsJson),
    ]
    const globalTalentDataDto = await globalTalent.fetch(globalTalentParamsJson);

    const [lieDataDto, maiDataDto, bossDataDto, smartdeerDataDto, lingDataDto] = await Promise.all(requestList)

    const listQueryParams = {
      lieDataDto, maiDataDto, smartdeerDataDto, bossDataDto, lingDataDto, globalTalentDataDto,
      queryDto: smartdeerParamsJson
    }

    const aggregateListData = await getMultiSearchTalentList(listQueryParams)
    lastSearchRank = aggregateListData.data.maxItpTalentRank
    totalTalentList.value = aggregateListData.data.data
    await updateTalentInfoForScore()

  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

const handleGoLogin = () => {
  let url = ''
  switch (activeTab.value) {
    case 'liepin':
      url = 'https://h.liepin.com/account/login'
      break;
    case 'maimai':
      url = 'https://maimai.cn/platform/login'
      break;
    case 'linkedin':
      url = 'https://www.linkedin.cn/login'
      break;
    case 'boss':
      url = 'https://www.zhipin.com/web/user/'
      break;
    default:
      break;
  }
  window.open(url)
}

async function handleClickTalent(item: any) {
  status.spinning = true
  try {
    setSearchBury({ action: 1, track_id: item.trackId })

    console.log('click: ', item)
    if (isNaN(Number(item.resumeUrl))) {
      if (item.site === 5) {
        await boss.beforeOpen(item.bossLid, item.bossExpectId, item.bossSecurityId)
      } else if (item.site === 2) {
        await liepin.beforeOpen(item)
        item.resumeUrl = liepin.getResumeUrl()
      }
      if (env === 'production') {
        if (item.resumeUrl.indexOf('linkedin') !== -1) {
          window.open(item.resumeUrl + '?icbTrackId=' + item.trackId)
        } else {
          window.open(item.resumeUrl + '&icbTrackId=' + item.trackId)
        }
      } else {
        window.open(item.resumeUrl)
      }
    } else {
      if (Number(item.site) === 1) {
        talentId.value = Number(item.resumeUrl)
        trackId.value = item.trackId
        status.showTalentDetail = true
      } else if (Number(item.site) === 6) {
        const res = await saveSmartDeerAppTalent(item.accountId)
        talentId.value = res.data.talent.id
        trackId.value = item.trackId
        status.showTalentDetail = true
      } else if (Number(item.site) === 7) {
        status.showGlobalTalentDetail = true
        globalTalentId.value = item.id
      } else {
        talentId.value = Number(item.resumeUrl)
        trackId.value = item.trackId
        status.showTalentDetail = true
      }
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.spinning = false
}

const chatUsers = reactive({
  from: {}, to: {}
})

const FROM_USER = 'smart:0:smartdeer'
function handleChat(props: any) {
  chatUsers.to = {
    imUserId: props.imUserId,
    nick: props.name,
    avatar: props.avatar
  }
  chatUsers.from = {
    imUserId: FROM_USER,
    nick: 'Jobs（乔布斯）',
    avatar: 'https://global-image.smartdeer.work/p/images/0x47b67a41f6324d2ea85c03270fe0064d.jpeg_median'
  }
  status.showChatBox = true
}

const handleRefreshPage = () => {
  location.reload()
}

function checkSiteLoginStatus() {
  console.log('aiSettings.value: ', aiSettings.value)
  if (!aiSettings.value || aiSettings.value.boss?.autoOpen === undefined || aiSettings.value.boss?.autoOpen === null || aiSettings.value.boss?.autoOpen === 1) {
    boss.checkLogin()
  }
  
  if (!aiSettings.value || aiSettings.value.maimai?.autoOpen === undefined || aiSettings.value.maimai?.autoOpen === null || aiSettings.value.maimai?.autoOpen === 1) {
    maimai.checkLogin()
  }
  
  if (!aiSettings.value || aiSettings.value.liepin?.autoOpen === undefined || aiSettings.value.liepin?.autoOpen === null || aiSettings.value.liepin?.autoOpen === 1) {
    liepin.checkLogin()
  }
  
  if (!aiSettings.value || aiSettings.value.linkedin?.autoOpen === undefined || aiSettings.value.linkedin?.autoOpen === null || aiSettings.value.linkedin?.autoOpen === 1) {
    linkedin.checkLogin()
  }
  
  smartdeer.checkLogin()
  itpTalent.checkLogin()
  globalTalent.checkLogin()
}

onMounted(async () => {
  try {
    const res = await getPushSetting()
    aiSettings.value = res.data
  } catch (err: any) {
    message.error('获取小推设置失败')
  }

  checkSiteLoginStatus()
  await initDict()
  if (!props.jobId) return
  status.showAIChatBox = true
})

</script>
<template lang="pug">
  mixin page-no-login
    .no-login
      div
        img(src="@/assets/noLogin.png")
      div 请先登录招聘网站，
        a-button(type="link" @click="handleGoLogin") 点此登录
      div 若已登录，
        a-button(type="link" @click="handleRefreshPage") 请刷新

  .talent-web-search
    a-spin(:spinning="status.initDict")

    .talent-list
      a-tabs(v-model:activeKey="activeTab" size="large")
        a-tab-pane(key="all")
          a-spin(:spinning="status.spinning")
            .talent-search(v-if="totalTalentList.length")
              TalentSearchItem(
                v-for="(item, index) in totalTalentList"
                :key="index"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent",
                @chat="() => handleChat(item)"
              )
              .search-pagination
                a-button(@click="handleTotalTalentNextPage" type="primary") 下一页
            a-empty(v-else)

          template(#tab)
            a-space
              span 全部聚合
              LoadingOutlined(v-if="status.spinning")
        a-tab-pane(key="globalTalent")
          a-spin(:spinning="globalTalent.loading")
            .talent-search(v-if="globalTalent.list.length")
              TalentGlobalSearchItem(
                v-for="(item, index) in globalTalent.list"
                :key="item.trackId"
                :item="item"
                :degree="dict.degreeMap"
                @click="handleClickTalent(item)"
                @open="handleClickTalent"
              )
              .search-pagination
                a-button(@click="globalTalent.nextPage()" type="primary") 下一页
            a-empty(v-else)
          template(#tab)
            a-space
              span ITP 人才库
              template(v-if="globalTalent.isLogin")
                LoadingOutlined(v-if="globalTalent.loading")
                .count(v-else) ({{ globalTalent.total }})
              .login(v-else) （请登录）
        a-tab-pane(key="liepin")
          template(v-if="!liepin.isLogin")
            +page-no-login
          template(v-else)
            a-spin(:spinning="liepin.loading")
              .talent-search(v-if="liepin.list.length")
                TalentSearchItem(
                  v-for="(item, index) in liepin.list"
                  :key="index"
                  :item="item"
                  :degree="dict.degreeMap"
                  @click="handleClickTalent(item)"
                  @chat="() => handleChat(item)"
                )
                .search-pagination
                  a-space(:size="12")
                    a-button(@click="liepin.prevPage()" type="primary" :disabled="liepin.page === 0") 上一页
                    a-button(@click="liepin.nextPage()" type="primary") 下一页
              a-empty(v-else)

          template(#tab)
            a-space
              span 猎聘
              template(v-if="liepin.isLogin")
                LoadingOutlined(v-if="liepin.loading")
                .count(v-else) ({{ liepin.total === 10000 ? '10000+' : liepin.total }})
              .login(v-else) （请登录）

        a-tab-pane(key="maimai")
          template(v-if="!maimai.isLogin")
            +page-no-login
          template(v-else)
            a-spin(:spinning="maimai.loading")
              .talent-search(v-if="maimai.list.length")
                TalentSearchItem(
                  v-for="(item, index) in maimai.list"
                  :key="index"
                  :item="item"
                  :degree="dict.degreeMap"
                  @click="handleClickTalent(item)"
                  @open="handleClickTalent"
                  @chat="() => handleChat(item)"
                )
                .search-pagination
                  a-space(:size="12")
                    a-button(@click="maimai.prevPage()" type="primary" :disabled="maimai.page === 0") 上一页
                    a-button(@click="maimai.nextPage()" type="primary" ) 下一页

              a-empty(v-else)

          template(#tab)
            a-space()
              span 脉脉
              template(v-if="maimai.isLogin")
                LoadingOutlined(v-if="maimai.loading")
                .count(v-else) ({{ maimai.total === 1000 ? '1000+' : maimai.total }})
              .login(v-else) （请登录）

        a-tab-pane(key="linkedin")
          template(v-if="false")
            +page-no-login
          template(v-else)
            a-spin(:spinning="linkedin.loading")
              .talent-search(v-if="linkedin.list.length")
                TalentSearchItem(
                  v-for="(item, index) in linkedin.list"
                  :key="index"
                  :item="item"
                  :degree="dict.degreeMap"
                  @click="handleClickTalent(item)"
                  @open="handleClickTalent"
                  @chat="() => handleChat(item)"
                )
                .search-pagination
                  a-space(:size="12")
                    a-button(@click="linkedin.prevPage()" type="primary" :disabled="linkedin.page === 0") 上一页
                    a-button(@click="linkedin.nextPage()" type="primary" ) 下一页

              a-empty(v-else)
          template(#tab)
            a-space
              span 领英
              template(v-if="linkedin.isLogin")
                LoadingOutlined(v-if="linkedin.loading")
                .count(v-else) ({{ linkedin.total === 1000 ? '1000+' : linkedin.total }})
              .login(v-else) （请登录）

        a-tab-pane(key="boss")
          template(v-if="!boss.isLogin")
            +page-no-login
          template(v-else)
            a-spin(:spinning="boss.loading")
              .talent-search(v-if="boss.list.length")
                TalentSearchItem(
                  v-for="(item, index) in boss.list"
                  :key="index"
                  :item="item"
                  :degree="dict.degreeMap"
                  @click="handleClickTalent(item)"
                  @open="handleClickTalent"
                  @chat="() => handleChat(item)"
                )
                .search-pagination
                  a-space(:size="12")
                    //- a-pagination(:total="boss.total")
                    a-button(@click="boss.prevPage()" type="primary" :disabled="boss.page === 0") 上一页
                    a-button(@click="boss.nextPage()" type="primary") 下一页

              a-empty(v-else)

          template(#tab)
            a-space()
              span Boss直聘
              template(v-if="boss.isLogin")
                LoadingOutlined(v-if="boss.loading")
                template(v-else)
                  template(v-if="boss.error")
                    a-tooltip
                      ExclamationCircleFilled(style="color: #F9470D;")
                      template(#title)
                        span {{ boss.error }}
                  template(v-else)
                    .count() ({{ boss.total }})
              template(v-else)
                .login 请登录

        a-tab-pane(key="smartdeer")
          template(v-if="!smartdeer.isLogin")
            +page-no-login
          template(v-else)
            a-spin(:spinning="smartdeer.loading")
              .talent-search(v-if="smartdeer.list.length")
                TalentSearchItem(
                  v-for="(item, index) in smartdeer.list"
                  :key="index"
                  :item="item"
                  :degree="dict.degreeMap"
                  @click="handleClickTalent(item)"
                  @open="handleClickTalent"
                  @chat="() => handleChat(item)"
                )
                .search-pagination
                  a-space(:size="12")
                    a-button(@click="smartdeer.prevPage()" type="primary" :disabled="smartdeer.page === 0") 上一页
                    a-button(@click="smartdeer.nextPage()" type="primary") 下一页
              a-empty(v-else)

          template(#tab)
            a-space()
              span SmartDeer
              LoadingOutlined(v-if="smartdeer.loading")
              .count(v-else) ({{ smartdeer.total }})

    a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
      TalentDetailModal(:talentId="talentId" :jobId="props.jobId" :trackId="trackId" @close="status.showTalentDetail = false")

    a-modal(v-model:open="status.showGlobalTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
      TalentGlobalDetailModal(:talentId="globalTalentId" :jobId="props.jobId" :trackId="trackId" @close="status.showGlobalTalentDetail = false")

    a-drawer(v-model:open="status.showChatBox" :destroyOnClose="true" :width="480" title="聊天记录" :bodyStyle="{padding: 0}")
      ChatBox(:from="chatUsers.from" :to="chatUsers.to")
  AIChatBox(
    v-if="status.showAIChatBox"
    logo=""
    title="小推"
    buttonImgUrl="https://static.smartdeer.work/logo.png",
    :jobId="props.jobId",
    :talentInfo="talentInfo",
    @search="(searchParams: any) => handleSearch(searchParams)"
    @showScore="(site: number, talentId: number, resumeId: any, score: any) => updateGlobalTalentScore(site, talentId, resumeId, score)"
  )
</template>

<style lang="scss" scoped>
.talent-web-search {
  width: 100%;
  height: 100%;
  padding-bottom: 32px;
}

.keyword-search {
  margin-bottom: 8px;
}

.talent-filter {
  background: #fff;
  padding: 20px 20px 4px;
  margin-bottom: 20px;
  border-radius: 8px;
  opacity: 100%;

  .talent-filter-item {
    display: flex;
    align-items: center;
    height: 100%;
    transition: all .3s;

    label {
      white-space: nowrap;
      margin-right: 8px;
      width: 65px;
      min-width: 65px;
      color: #777;
    }
  }
}

.no-login {
  text-align: center;
  padding-bottom: 80px;

  img {
    margin: 0 auto;
  }
}

.talent-list {
  border-radius: 8px;
  background: #fff;
  padding: 0 20px;

  .talent-search {
    .search-pagination {
      position: sticky;
      bottom: 0;
      background-color: #fff;
      padding: 16px;
      text-align: center;
    }
  }
}

.no-available {
  opacity: 20%;
  transition: all .3s
}
</style>