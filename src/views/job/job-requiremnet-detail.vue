<script setup lang="ts">
import dayjs from 'dayjs'

import { useRouter, useRoute } from 'vue-router'
import { onMounted, reactive, ref, computed, watch } from 'vue'
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import { getJobRequirementFollowUp, createJobRequirementFollowUp, getJobShareInfo } from '@/api/job'
import { getCustomerDetail } from '@/api/customer'
import { message, Drawer } from 'ant-design-vue'
import { useUserStore } from '@/store/user.store'
import { getJobStatus } from '@/api/dictionary'
import { getCompanyUserDetail } from '@/api/system/users'
import { getJobRequirementFinanceConfig } from '@/api/finance'

import PositionDetail from '@/components/app/position-detail.vue'
import PositionRequirement from '@/components/app/position-requirement.vue'
import PositionUpdate from '@/components/app/position-update.vue'
import InfoSection from '@/components/info-section/info-section.vue'
import JobUpdate from '@/components/app/job-update.vue'
import CustomerDetail from '@/components/app/customer-detail.vue'
import JobRequirementProgress from '@/components/app/job-requirement-progress.vue'
import { formatSalary } from '@/utils/salary-helper'
import CreateInvoice from '@/components/app/create-invoice.vue'
import CreatePayment from '@/components/app/create-payment-form.vue'
import JobPriority from '@/components/app/job-priority.vue'
import { toRef } from 'vue'
import JobShareFormModal from '@/components/app/job-share-form-modal.vue'
import { PERMISSIONS, hasPermission } from '@/utils/permission'
import tracker from '@/utils/tracker'
import { currencyUnit } from '@/views/h5/dict'
import JobRequirementInterviews from '@/components/app/job-requirement-interviews.vue'
import TalentGlobalSearch from '@/views/talent/talent-global-search.vue'
import TalentAiGlobalSearch from '@/views/talent/talent-ai-global-search.vue'
import JobRequirementTopTalents from '@/components/app/job-requirement-top-talents.vue'

const props = defineProps<{id: number}>()
const jobRequirementId = toRef(props, 'id')
const drawer = reactive({show: false,title: '修改职位信息'})
const router = useRouter()
const route = useRoute()
const selectedTab = ref(1)
const jobRequirementProgress = ref()
const userStore = useUserStore()
const sharedJob = ref(null)
const isAdmin = ref(userStore.isAdmin)

const status = reactive({
  positionDetailLoading: false,
  jobRequirementLoading: false,
  positionLoading: false,
  customerLoading: false,
  showPositionUpdate: false,
  showJobUpdate: false,
  showShareJobModal: false,

  showCreateInvoice: false,
  showCreatePayment: false,
  jobShareInfoLoading: false,

  isCurrentCompanyJob: false,
  hasShareJobPermission: hasPermission(PERMISSIONS.project.children.platform.code)
})

const jobRequirement = reactive({
  id: 0,
  status: 0,
  priority: 0,
  positionId: 0,
  position: {},
  customer: {},
  properties: [],
  processName: '',
  customerId: 0,
  customerName: '',
  positionStartDate: '',
  companyId: 0,
  canPublishToPlatform: false,
  pmUserDetail: <any>[]
})

const financeConfig = reactive<any>({
  paymentTimes: 0,
  paymentCycles: [],
  highestCommission: 0,
  steps: [],
  salaryUnit: 0,
  salaryUnitStr: ''
})

async function fetchJobShareInfo(jobId:number) {
  status.jobShareInfoLoading = true
  try {
    const res = await getJobShareInfo(jobId)
    sharedJob.value = res.data
  } catch(err:any) {
    console.log(err)
  }
  status.jobShareInfoLoading = false
}

async function fetchJobRequirementDetail(jobId: number) {
  status.jobRequirementLoading = true
  try {
    jobRequirement.id = jobId
    const res = await getJobRequirementDetail(jobId)
    // 这里可以拿到人员信息，即BD，PM，CA的信息
    jobRequirement.processName = res.data.processName
    jobRequirement.customerId = res.data.customerId
    jobRequirement.customerName = res.data.customerName
    jobRequirement.properties = res.data.properties
    jobRequirement.positionId = res.data.positionId
    jobRequirement.status = res.data.status
    jobRequirement.priority = res.data.priority
    jobRequirement.positionStartDate = res.data.positionStartDate
    jobRequirement.companyId = res.data.companyId
    // 有两种数据可能，一种是undifined， 一种是0或者1. undifined是历史数据。
    jobRequirement.canPublishToPlatform = res.data.canPublishToPlatform == 0 ? false : true

    jobRequirement.pmUserDetail = []
    jobRequirement.properties.forEach((item: any, index: number) => {
      if (item.key?.toLowerCase() === 'pm') {
        console.log(item)
        getCompanyUserDetail(item.value).then(res => {
          const pmUser = {
            userName: res.data.realName,
            formalPhotoUrl: res.data.formalPhotoUrl,
            wechatQrCodeUrl: res.data.wechatQrCodeUrl
          }
          if (res.data.wechatQrCodeUrl) {
            jobRequirement.pmUserDetail.push(pmUser)
          }
        })
      }
    })

    financeConfig.paymentTimes = 0
    financeConfig.paymentCycles = []
    financeConfig.highestCommission = 0
    financeConfig.steps = []
    financeConfig.salaryUnit = 0
    financeConfig.salaryUnitStr = ''
    try {
      const finance = await getJobRequirementFinanceConfig(jobId);
      buildCommissionDescription(finance.data)
    } catch (err){
      console.log(err)
    }

    console.log(financeConfig)

    fetchPositionDetail(res.data.positionId)
    fetchCustomerDetail(res.data.customerId)

    status.isCurrentCompanyJob = jobRequirement.companyId === userStore.companyId
    if (status.isCurrentCompanyJob && status.hasShareJobPermission) {
      await fetchJobShareInfo(Number(jobRequirementId.value))
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.jobRequirementLoading = false
}

// 客户详情部分
const customerDetail = ref<any>({})
async function fetchCustomerDetail(customerId: number) {
  status.customerLoading = true
  try {
    const res = await getCustomerDetail(customerId)
    customerDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.customerLoading = false
}

// 职位详情部分
const positionDetail = ref<any>({ tags: [] })
async function fetchPositionDetail(positionId: number) {
  status.positionLoading = true
  try {
    const res = await getPositionDetail(positionId)
    positionDetail.value = res.data
  } catch (err: any) {
    message.error(err.message)
  }
  status.positionLoading = false
}

function getUsers(type: 'bd' | 'pm' | 'ca') {
  const users: any[] = []
  jobRequirement.properties.forEach((item: any, index: number) => {
    if (item.key?.toLowerCase() === type) {
      users.push(item.valueName)
    }
  })
  return users.join(' / ')
}

function handlePositionUpdate() {
  fetchPositionDetail(jobRequirement.positionId)
  status.showPositionUpdate = false
}

// 这里只负责进行打点，不做业务
function handleTabChange(key: string) {
  tracker.click('job-detail-tab-click', { tab: key })
}

function handleJobUpdate() {
  fetchJobRequirementDetail(jobRequirement.id)
  status.showJobUpdate = false
}

const positionUpdateType = ref('')
function shwoPositionUpdate(type:string) {
  positionUpdateType.value = type
  status.showPositionUpdate = true
}

async function poolUpdate() {
  jobRequirementProgress.value.refresh()
}

function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

let jobStatusDict = [] as any[]
function getJobStatusStr(status: number) {
  const statusDict = jobStatusDict.find((element) => {
    return element.value === status
  })
  if (statusDict) return statusDict.label
  else return ''
}

function handleGotoCustomerDetail(customerId: number) {
  router.push(`/customer/${customerId}/detail`)
}

function buildCommissionDescription(data: any) {
  financeConfig.paymentTimes = data.paymentCycles.length
  data.paymentCycles.forEach((item: any) => {
    let processName = ''
    let processPeriod = ''
    let processExpiry = ''
    if (item.taskDefinitionKey === 'position_pm_talent_interview') {
      processName = '面试'
    } else if (item.taskDefinitionKey === 'position_pm_talent_to_offer') {
      processName = 'Offer 确认'
    } else if (item.taskDefinitionKey === 'position_pm_talent_to_hired') {
      processName = '入职'
    } else if (item.taskDefinitionKey === 'position_pm_talent_in_keep') {
      processName = '保证期'
    }
    if (item.onFinished) {
      processPeriod = '完成后'
    } else {
      processPeriod = '阶段开始后'
    }
    if (data.exceedConfirmDays > 0) {
      processExpiry = `${data.exceedConfirmDays}天内`
    }
    const cycleDesc = {
      name: item.name,
      desc: `候选人${processName}${processPeriod}${processExpiry}支付总佣金的${item.paymentPercent}%`
    }
    financeConfig.paymentCycles.push(cycleDesc)
  })
  const cycleCurrency = currencyUnit.get(data.currencyType)
  data.quotedPriceOnDelivery.forEach((item: any) => {
    let timeUnitStr = ''
    let insuranceTypeStr = ''
    let commissionStr = ''
    let salaryRangeStr = ''
    if (item.salaryFrom === 0 && item.salaryTo === -1) {
      salaryRangeStr = ``
    } else if (item.salaryFrom === 0 || item.salaryFrom === -1) {
      salaryRangeStr = ` ≤ ${item.salaryTo}`
    } else if (item.salaryTo === -1 || item.salaryTo === 0) {
      salaryRangeStr = ` ≥ ${item.salaryFrom}`
    } else if (item.salaryFrom > 0 && item.salaryTo > 0) {
      salaryRangeStr = `在 ${item.salaryFrom} - ${item.salaryTo} 之间`
    }
    if (item.commissionTimeUnit === 0) {
      timeUnitStr = "月薪"
    } else if (item.commissionTimeUnit === 1) {
      timeUnitStr = "日薪"
    } else if (item.commissionTimeUnit === 2) {
      timeUnitStr = '年薪'
    }
    if (item.percentageAgentFee) {
      commissionStr = `${timeUnitStr}的${Math.floor(item.percentageAgentFee)}%`
    } else {
      commissionStr = `固定金额${Math.floor(item.fixedAgentFee)}${cycleCurrency}`
    }
    if (item.insuranceType === 1) {
      insuranceTypeStr = `${item.insuranceValue}个月`
    } else {
      insuranceTypeStr = `${item.insuranceValue}天`
    }
    let commissionPreDesc = ``
    if (salaryRangeStr !== '') {
      commissionPreDesc = `当候选人的薪资${salaryRangeStr}${cycleCurrency}时，`
    }
    const stepDesc = {
      name : '',
      desc: `${commissionPreDesc}佣金为${commissionStr}，保证期${insuranceTypeStr}`
    }
    financeConfig.steps.push(stepDesc)
  })
}

function getCommissionStepsDesc () {
  let desc: any[] = []
  financeConfig.steps.forEach((item: any) => {
    desc.push(item.desc)
  })
  return desc.join('<br>')
}

function getCommissionCyclesDesc() {
  let desc: any[] = []
  financeConfig.paymentCycles.forEach((item: any) => {
    desc.push(item.desc)
  })
  return desc.join('<br>')
}

onMounted(async ()=>{
  const res = await getJobStatus()
  jobStatusDict = res.data
  fetchJobRequirementDetail(Number(jobRequirementId.value))
})

watch(()=>props.id, async (value, old)=>{
  fetchJobRequirementDetail(Number(value))
})

</script>

<template lang="pug">
mixin position-info-tabs
  .position-info
    a-spin(:spinning="status.jobRequirementLoading")
      a-tabs(v-model:activeKey="selectedTab" @change="handleTabChange")
        a-tab-pane(:key="1" tab="职位进展")
          //- 职位需求进展
          JobRequirementProgress(:jobRequirementId="jobRequirementId" :jobCompanyId="jobRequirement.companyId" ref="jobRequirementProgress")
        a-tab-pane(:key="5" tab="全网搜")
          //- 全网搜
          .talent-web-search-container
            TalentAiGlobalSearch(:jobId="jobRequirementId")
        a-tab-pane(:key="4" tab="职位详情")
          a-row.position-detail
            a-col(:span="12")
              a-spin(:spinning="status.positionLoading")
                PositionDetail(:position="positionDetail" :editable="status.isCurrentCompanyJob" @edit="()=>shwoPositionUpdate('positionInfo')" :show-customer="false")
            a-col(:span="12")
              a-spin(:spinning="status.positionLoading")
                PositionRequirement(:position="positionDetail" :editable="status.isCurrentCompanyJob" @edit="()=>shwoPositionUpdate('positionRequirement')")

        a-tab-pane(:key="6" tab="客户详情")
          .customer-detail
            CustomerDetail(:customerId="jobRequirement.customerId" :editable="false")
        a-tab-pane(:key="7" tab="面试记录")
          .interview-detail
            JobRequirementInterviews(:jobRequirementId="jobRequirementId" :jobCompanyId="jobRequirement.companyId")
        a-tab-pane(:key="8" tab="小推推荐人选")
          .ai-detail
            JobRequirementTopTalents(:jobRequirementId="jobRequirementId")

        template(#renderTabBar)
          a-space.position-info__head(:size="32")
            h4(@click="selectedTab = 5" :class="{'active': selectedTab === 5}") 全网搜
            h4(@click="selectedTab = 1" :class="{'active': selectedTab === 1}") 职位进展
            h4(@click="selectedTab = 4" :class="{'active': selectedTab === 4}") 职位详情
            h4(@click="selectedTab = 6" :class="{'active': selectedTab === 6}") 客户详情
            h4(@click="selectedTab = 7" :class="{'active': selectedTab === 7}") 面试记录
            h4(@click="selectedTab = 8" :class="{'active': selectedTab === 8}") 小推推荐人选

      a-drawer(v-model:open="status.showPositionUpdate" title="更新职位" :destroyOnClose="true" :width="480" :bodyStyle="{padding: 0}")
        PositionUpdate(:positionId="positionDetail.id" @update="handlePositionUpdate" @close="status.showPositionUpdate = false" :type="positionUpdateType")

    JobShareFormModal(:jobId="jobRequirementId" v-model:open="status.showShareJobModal" @update="fetchJobShareInfo")

mixin postion-summary
  .position-summary
    a-spin(:spinning="status.positionLoading")
      InfoSection(:editable="status.isCurrentCompanyJob" @edit="()=> {status.showJobUpdate = true}")
        .position-summary__head
          a-space(:size="0") 
            JobPriority(:priority="jobRequirement.priority")
            strong {{ positionDetail?.positionTitle || '-' }}
        .position-summary__item
          a-space(:size="[24, 0]" wrap) 
            div 
              span.label 状态: 
              span {{ getJobStatusStr(jobRequirement.status) }}
            div 
              span.label HC: 
              span {{ positionDetail?.quantityRequired }} 个
            div 
              span.label 工作地点: 
              span {{ positionDetail?.areaStr }}
            div 
              span.label 税前薪资: 
              span {{formatSalary(positionDetail)}}
            div 
              span.label 开始时间: 
              span {{jobRequirement.positionStartDate}}
        .position-summary__item
          a-space(:size="[24, 0]" wrap) 
            div 
              span.label BD: 
              span {{ getUsers('bd') }}
            div
              a-popover(placement="bottom")
                template(#content)
                  ul.user-detail
                    li.user-detail-item(v-for="(item, index) in jobRequirement.pmUserDetail")
                      .real-name 扫码添加企微，立即咨询
                      .wechat-code
                        img(:src="item.wechatQrCodeUrl")
                span.label PM:
                span {{ getUsers('pm') }}
            div 
              span.label CA: 
              span {{ getUsers('ca') }}
        .finance-config(v-if="financeConfig.paymentTimes > 0")
          h4 接单预计佣金说明：
          table.commission-notice
            tr
              td 佣金规则：
              td
                pre(v-html="getCommissionStepsDesc()")
            tr
              td 支付分期方式：
              td 共 {{ financeConfig.paymentTimes }} 期
            tr
              td 客户支付周期：
              td
                pre(v-html="getCommissionCyclesDesc()")
            tr(v-if="!status.isCurrentCompanyJob")
              td SmartDeer 支付周期：
              td 客户付款后 10 个工作日内

      a-drawer(v-model:open="status.showJobUpdate" title="更新项目" :destroyOnClose="true" :width="480" :bodyStyle="{padding: 0}")
        JobUpdate(:jobRequirementId="jobRequirement.id" @update="handleJobUpdate" @close="status.showJobUpdate = false")

mixin company-info
  .company-info
    a-spin(:spinning="status.customerLoading")
      InfoSection()
        .company-info__name
          a(@click="() => handleGotoCustomerDetail(customerDetail.id)" v-if="status.isCurrentCompanyJob")
            strong {{ customerDetail.customerFullName }}
          strong(v-else) {{ customerDetail.customerFullName }}
        .company-info__item(v-if="status.isCurrentCompanyJob && isAdmin")
          a-space(:size="[24, 0]" wrap)
            div
              span.label 公司所在地：
              span {{ customerDetail.areaStr }}
            div
              span.label 联系人：
              span {{ customerDetail.contactors && customerDetail.contactors.length ? customerDetail.contactors[0].name : '' }}
        .company-info__item(v-if="status.isCurrentCompanyJob && isAdmin")
          a-space(:size="[24, 0]" wrap)
            div
              span.label 联系电话：
              span {{ customerDetail.contactors && customerDetail.contactors.length ? customerDetail.contactors[0].phone : '' }}
            div
              span.label 微信号：
              span {{  customerDetail.contactors && customerDetail.contactors.length ? customerDetail.contactors[0].wechatNumber: '' }}
        .company-info__desc(v-if="!status.isCurrentCompanyJob || !isAdmin")
          span {{ customerDetail.customerIntroduction }}

mixin page-header
  a-page-header(title="职位详情", @back="()=>{$router.go(-1)}", style="padding: 0 0 8px;")
    template(#extra)
      //- 如果是当前公司的项目，且有分配项目的权限，且职位可以发布到平台，则显示分配项目按钮
      a-button(
        type="primary" 
        ghost
        @click="() => status.showShareJobModal = true"
        v-if="status.isCurrentCompanyJob && status.hasShareJobPermission"
        :loading="status.jobShareInfoLoading"
      ) 
        span(v-if="sharedJob") 修改项目分配
        span(v-else) 分配项目到其他公司
      a-button(type="primary" @click="() => status.showCreateInvoice = true" v-if="userStore.companyId==1") 申请开票

.job-requirement-detail-page
  +page-header
  .position-detail__mian
    a-row(:gutter="[16]")
      a-col(:span="16")
        +postion-summary
      a-col(:span="8")
        +company-info
    +position-info-tabs

  template(v-if="status.showCreateInvoice")
    CreateInvoice(
      :processName="jobRequirement.processName" 
      :id="jobRequirement.id" 
      :customerId="jobRequirement.customerId"
      @close="status.showCreateInvoice = false"
      @success="status.showCreateInvoice = false"
    )
  template(v-if="status.showCreatePayment")
    CreatePayment(
      :processName="jobRequirement.processName" 
      :id="jobRequirement.id" 
      :customerId="jobRequirement.customerId"
      :customerName="jobRequirement.customerName"
      @close="status.showCreatePayment = false"
      @success="status.showCreatePayment = false"
    )
</template>


<style lang="scss" scoped>
.job-requirement-detail-page {
  h2 {
    height: 33px;
    font-size: 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 20px;
    user-select: none;

    .anticon {
      font-size: 16px;
      opacity: 0.5;
      color: #8C8C8C;
      margin-right: 11px;
    }
  }
}

.position-info {
  margin-top: 20px;
  background: white;
  border-radius: 4px;

  &__head {
    padding: 24px 24px 12px 24px;
    display: flex;

    h4 {
      font-size: 20px;
      line-height: 28px;
      cursor: pointer;
      transition: all .2s;
      color: #ddd;
      margin: 0;

      &.active {
        color: #FF9111;
        transition: all .2s;
      }
    }
  }
}

.position-progress-item {
  display: flex;
  flex: 1 1 auto;
  cursor: pointer;
  transition: all .2s;

  &:hover {
    opacity: .6;
    transition: all .2s;
  }

  &__index {
    border-radius: 50%;
    height: 16px;
    width: 16px;
    line-height: 16px;
    text-align: center;
    background-color: #8C8C8C;
    color: #fff;
    font-size: 12px;
    margin-right: 8px;
  }

  &__title {
    line-height: 16px;
    font-size: 14px;
  }
}

.position-summary {
  background: #FFFFFF;
  border-radius: 8px;
  line-height: 32px;
  height: 100%;

  &__head {
    margin-bottom: 8px;
    position: relative;
    strong {
      font-size: 20px;
      display: block;
    }
  }

  span.label {
    color: #999;
  }
}

.company-info {
  background: white;
  border-radius: 8px;
  line-height: 32px;
  height: 100%;

  &__desc {
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
  }

  &__name {
    margin-bottom: 8px;
    font-size: 20px;
  }

  span.label {
    color: #999;
  }
}


.talent-web-search-container {
  border-top: 1px solid #f0f0f0;
  width: 100%;
}

.position-detail, .customer-detail {
  border-top: 1px solid #f0f0f0;
}
.user-detail {
  padding: 0;
  margin: 0;
  list-style: none;
}
.user-detail-item {
  padding: 0;
  padding-bottom: 10px;
  margin: 0;
  width: 200px;
  border-bottom: 1px #DDD solid;

  .avatar {
    display: inline-block;
    vertical-align: top;
    width: 80px;
    height: 80px;
    overflow: hidden;
    img {
      width: 80px;
      height: 80px;
    }
  }
  .real-name {
    display: inline-block;
    width: 200px;
    line-height: 26px;
  }
  .wechat-code {
    vertical-align: top;
    width: 120px;
    height: 120px;
    img {
      width: 120px;
      height: 120px;
    }
  }
}
.commission-notice {
  td {
    vertical-align: top;
  }
}
</style>