<template lang="pug">
.customer-create-page
  a-page-header(
    title="新增项目",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )

  a-spin(:spinning="status.initDict")
    .page-body
      a-form(:form="form" labelAlign="left" ref="formInstance" :model="form")
        a-row
          a-col(:span="12")
            .form-group
              .form-group-title
                h4 基础信息

              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label.required 客户
                a-col(:span="20")
                  a-form-item(
                    :colon="false"
                    name="customerId"
                    :rules="[{ type: 'number', required: true, message: '客户不能为空' }]"
                  )
                    a-select(
                      style="width:100%",
                      placeholder="请选择客户",
                      v-model:value="form.customerId",
                      show-search,
                      :filter-option="false",
                      @search="debouncedCustomerSearch",
                      :options="customerSearchList"
                      @change="handleChangeCustomer"
                    )
                      template(#notFoundContent)
                        template(v-if="status.fetchCustomer")
                          a-spin
                        template(v-else)
                          a-empty

              a-row(:gutter="16")
                a-col(:span="12")
                  a-row.form-item-line(:gutter="16")
                    a-col(:span="8")
                      .form-item-label.required 招聘数量
                    a-col(:span="16")
                      a-form-item(
                        name="quantityRequired"
                        :rules="[{ type: 'number', required: true, message: '招聘数量为必填项' }, { type: 'number', min: 1, message: '招聘数量须大于1' }]"
                      )
                        a-input-number(
                          v-model:value="form.quantityRequired",
                          style="width:100%",
                          placeholder="招聘数量,至少为1"
                        )

              a-row(:gutter="12")
                a-col(:span="12")
                  a-row.form-item-line(:gutter="16")
                    a-col(:span="8")
                      .form-item-label.required 开始日期
                    a-col(:span="16")
                      a-form-item(
                        name="startDateObject"
                        :rules="[{ required: true, message: '客户所属行业不能为空' }]"
                      )
                        a-date-picker(v-model:value="form.startDateObject")
                a-col(:span="12")

                  a-row.form-item-line(:gutter="16")
                    a-col(:span="8")
                      .form-item-label 优先级
                    a-col(:span="16")
                      a-form-item(
                        name="priority"
                        :rules="[{ type: 'number', required: false }]"
                      )
                        a-select(v-model:value="form.priority")
                          a-select-option(:value="10") 最高优先级(P0)
                          a-select-option(:value="5") 常规(P1)
                          a-select-option(:value="1") 低优先级(P2)

              a-row.form-item-line(:gutter="16")
                a-col(:span="6")
                  .form-item-label 可发布到三方平台
                a-col(:span="18")
                  a-form-item(
                    name="canPublishToPlatform"
                  )
                    a-radio-group(v-model:value="form.canPublishToPlatform")
                      a-radio(:value="1") 是
                      a-radio(:value="0") 否

          a-col(:span="12")
            .form-group
              .form-group-title
              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label.required BD
                a-col(:span="20")
                  a-form-item(
                    :colon="false"
                    name="BDUsers"
                    :rules="[{ required: true, message: '请至少指定一名BD' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-select(
                      show-search
                      placeholder="请指定BD"
                      :disabled="!!companyUserId"
                      v-model:value="form.BDUsers"
                      :options="dict.BDUsers"
                      :filter-option="filterOption"
                    )
              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label.required PM
                a-col(:span="20")
                  a-form-item(
                    name="PMUsers"
                    :rules="[{ required: true, message: '请至少指定一名PM' }]"
                  )
                    a-select(
                      show-search
                      placeholder="请指定PM",
                      v-model:value="form.PMUsers",
                      mode="multiple",
                      :options="dict.PMUsers",
                      :filter-option="filterOption"
                    )
              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label.required CA
                a-col(:span="20")
                  a-form-item(
                    name="CAUsers"
                    :rules="[{ required: true, message: '请至少指定一名CA' }]"
                  )
                    a-select(
                      show-search
                      placeholder="请指定CA",
                      v-model:value="form.CAUsers",
                      mode="multiple",
                      :options="dict.CAUsers",
                      :filter-option="filterOption"
                    )

        a-row()
          a-col(:span="12")
            .form-group
              .form-group-title
                h4 职位信息
              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label.required 职位名称
                a-col(:span="20")
                  a-form-item(
                    name="positionTitle"
                    :rules="[{ type: 'string', required: true, message: '职位名称不能为空' }]"
                  )
                    a-input(
                      placeholder="请输入职位名称",
                      v-model:value="form.positionTitle",
                    )
                      template(#addonAfter)
                        a-select(
                          v-model:value="form.type"
                        )
                          a-select-option(:value="1") 社招
                          a-select-option(:value="2") 校招
                          a-select-option(:value="3") 实习

              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label 保密职位
                a-col(:span="20")
                  a-form-item(
                    name="confidential"
                  )
                    a-radio-group(v-model:value="form.confidential")
                      a-radio(:value="1") 是
                      a-radio(:value="0") 否

              a-row.form-item-line(:gutter="16")
                a-col(:span="4")
                  .form-item-label(:class="{required: !form.isRemote}") 工作地点
                a-col(:span="10")
                  a-form-item(
                    :colon="false"
                    name="areaId"
                    :rules="[{ required: !form.isRemote, message: '工作地点不能为空' }]"
                  )
                    //- 这里scale传从接口来的index
                    a-tree-select(
                      :fieldNames="{ label: 'title', value: 'id' }",
                      placeholder="请选择该职位的工作地点",
                      v-model:value="form.areaId",
                      :tree-data="dict.areas",
                      treeNodeFilterProp="title"
                      multiple
                    )
                a-col(:span="10")
                  a-form-item(
                    :colon="false"
                    name="isRemote"
                  )
                    a-checkbox(
                      v-model:checked="form.isRemote"
                    ) 远程工作

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 薪资范围
                a-col(:span="20")
                  a-row(:gutter="16")
                    a-col(:span="8")
                      a-form-item(
                        name="salaryFromK",
                        :rules=`[{ asyncValidator: salaryFromKValidator}]`
                      )
                        a-input(suffix="k" v-model:value="form.salaryFromK" style="width:100%" placeholder="最低")
                    a-col(:span="8")
                      a-form-item(
                        name="salaryToK",
                        :rules=`[{ asyncValidator: salaryToKValidator}]`,
                      )
                        a-input(suffix="k" v-model:value="form.salaryToK" style="width:100%"  placeholder="最高")
                          //- template(#addonAfter)

                    a-col(:span="8")
                      a-select(
                        show-search
                        style="width:100%;"
                        v-model:value="form.salaryUnit" 
                        :dropdownMatchSelectWidth="false"
                        :options="dict.CurrencyList"
                        :filter-option="filterOption"
                      )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 计薪方式
                a-col(:span="20")
                  a-row(:gutter="[16,16]")
                    a-col(:span="12")
                      a-form-item(                        
                        name="salaryTimeUnit",
                        :rules="[{required: true}]"
                      )
                        a-select(
                          v-model:value="form.salaryTimeUnit",
                          @change="(value:number)=>{ form.salaryTime = value!==0 ? 1 : form.salaryTime }",
                        )
                          a-select-option(:value="0") 月薪
                          a-select-option(:value="1") 日薪
                          a-select-option(:value="2") 年薪

                    a-col(:span="12" v-if="form.salaryTimeUnit === 0")
                      a-form-item(                        
                        name="salaryTime",
                        :rules=`[{ asyncValidator: salaryCalcMonthShouldAbove12 }]`)
                        a-input(v-model:value="form.salaryTime" style="width:100%" suffix="个月")

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 职能
                a-col(:span="20")
                  a-form-item(
                    :label-col="{ span: 8 }",
                    :wrappercol="{ span: 16 }"
                    :colon="false"
                    name="functionId"
                    :rules="[{ type: 'number', required: true, message: '请选择该职位职能' }]"
                  )
                    a-select(
                      placeholder="请选择该职位的职能",
                      v-model:value="form.functionId",
                      show-search,
                      :filterOption="filterOption",
                      :options="dict.function",
                    )


              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 管理岗
                a-col(:span="8")
                  a-form-item()
                    a-radio-group(v-model:value="form.isManager")
                      a-radio(:value="true") 是
                      a-radio(:value="false") 否
                a-col(:span="12")
                  a-form-item(
                    v-if="form.isManager == true"
                    label="下属人数"
                  )
                    a-input-number(:min="0" v-model:value="form.underlingNumber" style="width:100%")

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 面试流程
                a-col(:span="20")
                  a-form-item(
                    name="interviewProcess"
                    :rules="[{ type: 'string', required: true, message: '面试流程为必填选项' }]"
                  )
                    a-textarea(v-model:value="form.interviewProcess" :rows="4")

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label.required 职位描述
                a-col(:span="20")
                  a-form-item(
                    name="workDetail"
                    :rules="[{ type: 'string', required: true, message: '职位描述为必填选项' }]"
                  )
                    a-textarea(v-model:value="form.workDetail"  :rows="4")

          a-col(:span="12")
            .form-group
              .form-group-title
                h4 人才要求
              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 性别要求
                a-col(:span="20")
                  a-form-item(name="requireGender")
                    a-radio-group(v-model:value="form.requireGender")
                      a-radio(:value="0") 不限
                      a-radio(:value="1") 男
                      a-radio(:value="2") 女

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 年龄要求
                a-col(:span="20")
                  a-row(:gutter="16") 
                    a-col(:span="12")
                      a-form-item()
                        a-input-number(
                          style="width:100%" 
                          :min="ageRequire.min ? 18 : 0" max="80" 
                          placeholder="不限" 
                          v-model:value="ageRequire.min"
                        )
                    a-col(:span="12")
                      a-form-item()
                        a-input-number(
                          style="width:100%" 
                          :min="ageRequire.min || 18" max="80" 
                          v-model:value="ageRequire.max"
                          placeholder="不限" 
                        )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 经验要求
                a-col(:span="20")
                  //- a-row(:gutter="16") 
                  //-   a-col(:span="12")
                  //-     a-form-item(name="requireWorkYearsFrom",)
                  //-       a-input-number(suffix="年" style="width:100%" placeholder="不限" v-model:value="form.requireWorkYearsFrom" )
                  //-   a-col(:span="12")
                  //-     a-form-item(name="requireWorkYearsTo",)
                  //-       a-input-number(suffix="年" style="width:100%"  v-model:value="form.requireWorkYearsTo"  placeholder="不限")
                  a-form-item(name="requireWorkYears" :rules="[{ required: false, message: '请选择经验要求' }]")
                    a-select(
                      placeholder="请选择经验要求",
                      v-model:value="form.requireWorkYears",
                      :options="dict.workYears",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 学历要求
                a-col(:span="20")
                  a-form-item(
                    :rules="[{ required: false, message: '请选择学历要求' }]"
                  )
                    a-select(
                      placeholder="请选择学历要求",
                      v-model:value="form.requireDegree",
                      :options="dict.degree"
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 学校要求
                a-col(:span="20")
                  a-form-item(
                    name="requireEliteSchool"
                    :rules="[{ required: false, message: '请选择学校要求' }]"
                  )
                    a-select(
                      v-model:value="form.requireEliteSchool",
                      placeholder="请选择学校要求",
                      :options="dict.eliteSchool",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 对标公司
                a-col(:span="20")
                  a-form-item()
                    a-select(
                      v-model:value="form.targetFirmsArray"
                      mode="tags"
                      style="width: 100%"
                      :token-separators="[',','，', ' ']",
                      placeholder="请输入对标公司",
                      :notFoundContent="''"
                    )

              //- a-row.form-item-line
              //-   a-col(:span="4")
              //-     .form-item-label 管理岗
              //-   a-col(:span="6")
              //-     a-form-item(
              //-       :label-col="{ span: 8 }",
              //-       :wrappercol="{ span: 16 }"
              //-     )
              //-       a-radio-group(v-model:value="form.isManager")
              //-         a-radio(:value="1") 是
              //-         a-radio(:value="2") 否
              //-   a-col(:span="14")
              //-     a-form-item(
              //-       label="下属人数"
              //-       :label-col="{ span: 8 }",
              //-       :wrappercol="{ span: 16 }"
              //-       v-if="form.isManager === 1"
              //-     )
              //-       a-input-number(:min="0" v-model:value="form.underlingNumber" style="width:100%")

              //- a-row.form-item-line
              //-   a-col(:span="4")
              //-     .form-item-label 团队规模
              //-   a-col(:span="20")
              //-     a-form-item(
              //-       :label-col="{ span: 8 }",
              //-       :wrappercol="{ span: 16 }"
              //-       name="teamScale"
              //-       :rules="[{ type: 'number', min: 1, message: '团队规模人数应大于0' }]"
              //-     )
              //-       a-input-number(v-model:value="form.teamScale" style="width:100%")

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 偏好标签
                a-col(:span="20")
                  a-form-item()
                    a-select(
                      v-model:value="form.tags"
                      mode="tags"
                      style="width: 100%"
                      :token-separators="[',','，', ' ']",
                      placeholder="请输入偏好标签",
                    )

              a-row.form-item-line
                a-col(:span="4")
                  .form-item-label 任职要求
                a-col(:span="20")
                  a-form-item(
                    name="workRequirement"
                    :rules="[{ type: 'string', required: false, message: '面试流程为必填选项' }]"
                  )
                    a-textarea(v-model:value="form.workRequirement" :rows="4")

        a-affix(:offset-bottom="0")
          .form-action
            a-space()
              // a-button(type="primary" ghost) 保存草稿
              a-button(@click="save" type="primary" :loading="status.loading") 新增职位
</template>

<script lang="ts" setup>

import { PlusOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { getWorkYearList, getCompanyFundingStageList, getAllFunctionList, dictionary, getCurrencyList } from '@/api/dictionary'
import { reactive, ref, nextTick, onMounted, watch } from 'vue'
import { getCompanyRoles, getCompanyOnboardUsers, getCompanyUsersByRoleName } from '@/api/system/roles'
import { createJobRequirement } from '@/api/position'
import { getCustomerList, getCustomerDetail } from '@/api/customer'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { debounce } from '@/utils/util'
import { areaDictToTreeData } from '@/utils/form-data-helper'

const router = useRouter()
function goBack() {
  router.go(-1)
}

const formInstance = ref()

const form = ref({
  areaId: [],
  customerId: null,
  functionId: null,
  positionTitle: '',
  priority: 5,
  quantityRequired: 1,
  requireAgeFrom: 0,
  requireAgeTo: 0,
  requireDegree: 0,
  requireEliteSchool: null,
  requireEnglishLevel: null,
  requireWorkYears: 0,

  requireWorkYearsFrom: null,
  requireWorkYearsTo: null,
  confidential: 0,
  canPublishToPlatform: 1,

  interviewProcess: '',
  status: 1,

  salaryTime: 12,
  salaryTimeUnit: 0,
  salaryFromK: 0,
  salaryFrom: 0,
  salaryToK: 0,
  salaryTo: 0,
  salaryUnit: 0,

  startDate: '',
  teamScale: null,
  workDetail: '',
  workRequirement: '',
  targetFirms: null as null | string,
  targetFirmsArray: [],
  isManager: false,
  positionUsers: [] as { roleName: string, companyUserId: number }[],
  BDUsers: '',
  CAUsers: [],
  PMUsers: [],
  startDateObject: dayjs(),
  underlingNumber: null,
  isRemote: false,
  remoteWork: 0,
  type: 1,
  requireGender: 0,

  positionContacts: [{
    name: '',
    department: '',
    phone: '',
  }],
  tags: [] as string[]
})
const companyUserId = ref(0)

const dict = reactive({
  areas: [] as any,
  function: [],
  degree: [] as any[],
  eliteSchool: [{ label: '无要求', value: null }, { label: '211', value: 1 }, { label: '985', value: 2 }, { label: 'QS200', value: 4 }, { label: '211 & 985', value: 3 }, { label: '211 & 985 & QS200', value: 7 }],
  englishLevel: [{ label: '未知', value: 0 }, { label: '不限', value: 1 }, { label: '听说读写熟练', value: 2 }, { label: '日常交流', value: 3 }, { label: '阅读资料文献', value: 4 }],
  workYears: [] as any[],
  BDUsers: [{ label: 'aa', value: 1 }, { label: 'ab', value: 2 }, { label: 'ac', value: 3 }],
  PMUsers: [{ label: 'ba', value: 1 }, { label: 'bb', value: 2 }, { label: 'bc', value: 3 }],
  CAUsers: [{ label: 'ca', value: 1 }, { label: 'cb', value: 2 }, { label: 'cc', value: 3 }],
  positionType: [{ label: '实习', value: 0 }, { label: '校招', value: 1 }, { label: '社招', value: 3 }],
  CurrencyList: [] as any,
})

const status = reactive({
  initDict: false,
  loading: false,
  fetchCustomer: false,
  editTags: false,
})

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

async function initDict() {
  status.initDict = true
  try {

    const [
      dictArea, dictDegree, dictWorkYear, dictFunction,
      dictCurrencyList, dictBDUsers, dictPMUsers, dictCAUsers
    ] = await Promise.all([
      dictionary.getAllAreaList(), dictionary.getDegree(), getWorkYearList(), getAllFunctionList(),
      getCurrencyList(), getCompanyOnboardUsers('bd'), getCompanyOnboardUsers('pm'), getCompanyOnboardUsers('ca'),
    ])

    const [areaTreeData, areaMap] = areaDictToTreeData(dictArea.data, false)
    dict.areas = areaTreeData

    dict.function = dictFunction.data.map((item: any, index: number) => { return { value: item.id, label: item.name } })
    dict.BDUsers = dictBDUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.CAUsers = dictCAUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.PMUsers = dictPMUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.CurrencyList = dictCurrencyList.data.map((item: any, index: number) => { return { value: item.id, label: item.name } })

    for (let id in dictDegree.data) {
      dict.degree.push({ value: Number(id), label: dictDegree.data[id] })
    }

    dict.workYears.push({ value: 0, label: '未知' })
    for (let id in dictWorkYear.data) {
      dict.workYears.push({ value: Number(id), label: dictWorkYear.data[id] })
    }

  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

const customerSearchList = ref<any[]>([])
async function fetchCustomer(keyword: string) {
  customerSearchList.value = []
  status.fetchCustomer = true
  try {
    const res = await getCustomerList({ keyWord: keyword })
    customerSearchList.value = res.data.customers.map((item: any, index: number) => {
      return { label: item.customerFullName, value: item.id, companyUserId: item.companyUserId, compnayUserName: item.companyUserStr }
    })
  } catch (err: any) {
    message.error(err.message)
  }
  status.fetchCustomer = false
}

const debouncedCustomerSearch = debounce(fetchCustomer)

// 切换客户
const handleChangeCustomer = (val: number) => {
  const customer: any = customerSearchList.value.find((item: any) => {
    return item.value === val
  })


  if (customer.companyUserId === 0) {
    form.value.BDUsers = ''
    companyUserId.value = 0
  } else {

    if (!dict.BDUsers.some((user) => user.value === customer.companyUserId)) {
      dict.BDUsers.push({ value: customer.companyUserId, label: customer.compnayUserName })
    }
    form.value.BDUsers = customer.companyUserId
    companyUserId.value = customer.companyUserId
  }
  getCustomerContactDetail(val)
}

async function getCustomerContactDetail(customerId: number) {
  const res = await getCustomerDetail(customerId)
  if (res.data.contactors.length !== 0) {
    const firstContact = res.data.contactors[0]
    form.value.positionContacts[0].name = firstContact.name
    form.value.positionContacts[0].phone = firstContact.phone
    form.value.positionContacts[0].department = firstContact.department
  }
}

const ageRequire = reactive<{ min: null | number, max: null | number }>({
  min: null,
  max: null
})

async function save() {
  status.loading = true
  try {
    await formInstance.value.validate()

    form.value.requireAgeFrom = ageRequire.min || 0
    form.value.requireAgeTo = ageRequire.max || 0

    form.value.positionUsers.push({ roleName: 'bd', companyUserId: Number(form.value.BDUsers) })

    form.value.PMUsers.forEach((item: any, index: number) => {
      form.value.positionUsers.push({ roleName: 'pm', companyUserId: item })
    })
    form.value.CAUsers.forEach((item: any, index: number) => {
      form.value.positionUsers.push({ roleName: 'ca', companyUserId: item })
    })

    form.value.startDate = form.value.startDateObject.format('YYYY-MM-DD')
    form.value.targetFirms = form.value.targetFirmsArray.join(',')
    form.value.salaryFrom = Number(form.value.salaryFromK) * 1000
    form.value.salaryTo = Number(form.value.salaryToK) * 1000

    form.value.remoteWork = form.value.isRemote ? 1 : 0

    debugger
    const res = await createJobRequirement(form.value)
    // 这里在保存成功后reset form
    formInstance.value.resetFields()
    formInstance.value.clearValidate()
    form.value.positionUsers = []

    router.push(`/job/${res.data.jobRequirementId}/save/success`)
  } catch (err: any) {
    if (err.errorFields && err.errorFields.length > 0) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

function deleteTag(index: number) {
  form.value.tags.splice(index, 1)
}

const inputTag = ref<string | null>('')
const tagInputInstance = ref()
function addNewTag() {
  status.editTags = false
  if (inputTag.value) form.value.tags.push(inputTag.value)
  inputTag.value = null
  tagInputInstance.value.focus()
}

function tagEdit() {
  status.editTags = true
  nextTick(() => {
    tagInputInstance.value.focus()
  })
}

function salaryCalcMonthShouldAbove12(rule: any, value: string, callback: Function) {
  if (value === '') callback();
  if (!/^[\d]*$/.test(value)) callback(new Error('请填写数值'))
  if (value && Number(value) >= 12) callback()
  else callback(new Error('计算月数应大于等于12'))
}

function salaryFromKValidator(rule: any, value: string, callback: Function) {
  try {
    if (value === '') {
      callback(new Error('请填写职位最小薪资'))
      return
    }
    if (Number(value).toString() === 'NaN') {
      callback(new Error('请填写数值'))
      return
    }
    const from = Number(form.value.salaryFromK)
    const to = Number(form.value.salaryToK)
    if (to && from > to) {
      callback(new Error('最小薪资应该小于最大薪资'))
      return
    }
    callback()
  } catch (err) {
    callback(new Error('请填写数值'))
  }
}

function salaryToKValidator(rule: any, value: string, callback: Function) {
  try {
    if (value === '') {
      callback(new Error('请填写职位最大薪资'))
      return
    }
    if (Number(value).toString() === 'NaN') {
      callback(new Error('请填写数值'))
      return
    }
    const from = Number(form.value.salaryFromK)
    const to = Number(form.value.salaryToK)
    if (from && to < from) {
      callback(new Error('最大薪资应大于最小薪资'))
      return
    }
    callback()
  } catch (err) {
    callback(new Error('请填写数值'))
  }
}

onMounted(() => {
  initDict()
})

</script>

<style lang="scss" scoped>
.page-body {
  border-radius: 8px;
  background-color: #fff;

  .form-group {
    padding: 20px;

    .company-contact {
      margin-bottom: 32px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
    }

    .form-group-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      min-height: 32px;

      h4 {
        line-height: 20px;
        font-size: 18px;
        font-weight: bold;
        position: relative;
        padding-left: 16px;

        &::before {
          content: "";
          display: block;
          background-color: #ff9111;
          height: 20px;
          width: 4px;
          top: 0;
          left: 0;
          border-radius: 2px;
          position: absolute;
        }
      }
    }

    .form-item-line {
      line-height: 32px;

      .form-item-action {
        text-align: right;

      }

      .upload-desc {
        color: #999;
        font-size: 12px;
        margin-top: 8px;
      }

      .form-item-label {
        white-space: nowrap;

        &.required {
          &::after {
            content: ' *';
            color: #ff9111;
          }
        }
      }
    }
  }

  .form-action {
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px;
    text-align: right;
  }
}
</style>