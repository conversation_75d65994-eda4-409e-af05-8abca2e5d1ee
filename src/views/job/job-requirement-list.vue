<script setup lang="ts">
import { getAreaList, getJobStatus, dictionary, getAllFunctionList, getAllIndustryList } from "@/api/dictionary"
// import { reactive } from "@vue/reactivity"
import { ref, onMounted, reactive, onActivated, toRef } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { getJobRequireHeaderIndex, getJobRequireSearch, POSITION_STATUS, QUERY_ACTION } from '@/api/job'
import { getCompanyRoles, getCompanyOnboardUsers } from '@/api/system/roles'
import { areaDictToTreeData, industryDictToTreeData } from '@/utils/form-data-helper'
import { getCustomerList } from "@/api/customer"
import JobPriority from "@/components/app/job-priority.vue"
import dayjs from "dayjs"
import { useUserStore } from "@/store/user.store"
const userStore = useUserStore()

interface OAny {
  [propName: string]: any;
}

const router = useRouter()
const route = useRoute()

const { priority, baseAreaType, remoteWork, toPM30Day, interviewed30Day } = route.query;

// 检索条件
const queryParams = reactive({
  customerId: '',
  name: '',
  status: POSITION_STATUS.ONGOING,
  locationIds: [] as number[],
  operators: [] as any[],
  priority: priority,
  functionId: '',
  industryIds: [] as number[],
  baseAreaType: baseAreaType,
  remoteWork: remoteWork,
  toPM30Day: toPM30Day,
  interviewed30Day: interviewed30Day
})

// 用于渲染filter
const filter = reactive({
  customer: [] as any,
  areas: [] as any,
  areaMap: null as any,
  roles: [] as any[],
  jobStatus: [] as any,
  function: [] as any,
  industry: [] as any,
  industryMap: {} as any,
})


const status = reactive({
  initFilter: false,
  searchLoading: false
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
})

const dataList = ref([]);

onMounted(() => {
  initTableColumnConfig()
})

// 初始化Filter
const initFilter = async () => {
  status.initFilter = true
  try {
    // role selector init
    const roleList = ['BD', 'PM', 'CA']
    const roleListMap = new Map()
    const roleStaffRequestList: Promise<any>[] = []

    let index = 0
    let roles: any[] = []
    roleList.forEach((role: any) => {
      if (['bd', 'ca', 'pm'].includes(role.toLowerCase())) {
        const roleItem = Object.assign({}, {name: role}, { staffList: [] as any[], value: null as any | null })
        // 初始化form item
        queryParams.operators.push(roleItem)
        roleListMap.set(index++, roleItem)
        roles.push(roleItem)
        roleStaffRequestList.push(getCompanyOnboardUsers(role))
      }
    })

    const staffResponseList = await Promise.all(roleStaffRequestList)

    staffResponseList.forEach((staffRes, index) => {
      // const role = roleListMap.get(index)
      roles[index].staffList = staffRes.data
      // role.staffList = staffRes.data
    })

    filter.roles = roles

    // area list init
    const areaDictRes = await dictionary.getAllAreaList()
    const [areaFormData, areaMap] = areaDictToTreeData(areaDictRes.data, true)
    filter.areas = areaFormData
    filter.areaMap = areaMap

    const industryDictRes = await getAllIndustryList();
    const [industryFormData, industryMap] = industryDictToTreeData(industryDictRes.data)
    filter.industry = industryFormData
    filter.industryMap = industryMap

    const dictJobStatus = await getJobStatus()
    filter.jobStatus = dictJobStatus.data

    fetchCustomerList()

  } catch (err) {
    message.error('初始化筛选器失败，请重试。')
  }
  status.initFilter = false
}
initFilter()

const _getAllFunctionList = async () => {
  const res = await getAllFunctionList()

  const list = res.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id
    }
  })
  
  filter.function = list
}
_getAllFunctionList()

const _getAllIndustryList = async () => {
  const res = await getAllIndustryList()

  const [industryFormData, industryMap] = industryDictToTreeData(res.data)
  filter.industry = industryFormData
  filter.industryMap = industryMap
}
_getAllIndustryList()

const fetchCustomerList = async (name: string = '') => {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    isSearchProject: true,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })

  filter.customer = list
}


const columnsList = ref([]);
const initTableColumnConfig = async () => {
  const { data } = await getJobRequireHeaderIndex()

  const nData = data.filter((item: any) => {
    return !['pm', 'bd', 'ca'].includes(item.key)
  })

  const list = nData.map((item: any, index: number) => {
    const key = item.key ? item.key.toLocaleLowerCase() : `key_${index}`
    const column = {
      ...item,
      title: item.name,
      key: key,
      dataIndex: key
    }

    if (item.key === 'jobTitle') return Object.assign({}, column, { fixed: 'left', width: 200 })
    if (item.key === 'hireCount') return Object.assign({}, column, { width: 90 })
    else return column
  })

  list.splice(2, 0, {title: '开始时间', key: 'key_startDate', dataIndex: 'positionStartDate', width: 120})
  list.push({ title: '操作', key: 'key_action', dataIndex: 'key_action', fixed: 'right', width: 100 })
  list.unshift({ title: '', key: 'key_priority', dataIndex: 'key_priority', fixed: 'left', width: 60 })
  columnsList.value = list
}

// 获取职位列表 
async function getJobList(queryParams: any) {
  status.searchLoading = true
  try {
    const { data } = await getJobRequireSearch(Object.assign({}, queryParams, { current: pagination.current, size: pagination.pageSize }))
    pagination.total = data.total
    const list = data.jobRequirements.map((item: any) => {
      let o: any = {}
      item.properties.forEach((row: any) => {
        const key = row.key.toLocaleLowerCase()
        if (o[key] === undefined) {
          o[key] = row.valueName
        } else {
          o[key] = `${o[key]},${row.valueName}`
        }
      })
      o['customerName'] = item.customerName
      o['key_action'] = '查看详情'
      o['id'] = item.id
      o['priority'] = item.priority
      o['positionStartDate'] = item.positionStartDate
      o['isFromSmartDeer'] = (item.companyId !== userStore.companyId)
      return o
    })
    
    dataList.value = list
  } catch (err: any) {
    message.error(`查询错误，请稍后重试 [${err.message}]`)
  }
  status.searchLoading = false
}


function getTimeSpan(time: string) {
  const now = dayjs()
  const startTime = dayjs(time)
  const diff = now.diff(startTime, 'day')
  return diff
}

function assembleQueryActions() {
  const queryInfo = []

  if (queryParams.locationIds.length) {
    queryInfo.push({
      key: 'locationId',
      action: QUERY_ACTION.IN,
      valueType: 'list',
      value: queryParams.locationIds.join(',')
    })
  }
  if (queryParams.industryIds.length) {
    queryInfo.push({
      key: 'industryId',
      action: QUERY_ACTION.IN,
      valueType: 'list',
      value: queryParams.industryIds.join(',')
    })
  }

  queryParams.operators.forEach((item, index) => {
    if (['pm', 'bd', 'ca'].includes(item.name.toLowerCase())) {
      if (item.value) {
        queryInfo.push({
          valueType: 'long',
          key: item.name.toLowerCase(),
          action: QUERY_ACTION.EQ,
          value: item.value
        })
      }
    }
  })

  console.log(queryParams.toPM30Day)

  if (queryParams.toPM30Day) {
    queryInfo.push({
      valueType: 'long',
      key: 'toPM30Day',
      action: QUERY_ACTION.EQ,
      value: '0'
    })
  }

  if (queryParams.interviewed30Day) {
    queryInfo.push({
      valueType: 'long',
      key: 'interviewed30Day',
      action: QUERY_ACTION.EQ,
      value: '0'
    })
  }

  return {
    name: queryParams.name,
    status: queryParams.status,
    priority: queryParams.priority,
    queries: queryInfo,
    customerId: queryParams.customerId,
    functionId: queryParams.functionId,
    baseAreaType: queryParams.baseAreaType,
    remoteWork: queryParams.remoteWork
  }
}

// Filter 切换 
function handleFilterChange(type: any, value: any) {
  switch (type) {
    case 'areas':
      const areaItem = filter.areaMap.get(value)
      if (areaItem) queryParams.locationIds = [areaItem.id].concat(areaItem.children.map((item: any) => item.id))
      else queryParams.locationIds = []
      break
    case 'industry':
      const industryItem = filter.industryMap.get(value)
      if (industryItem) queryParams.industryIds = [industryItem.id].concat(industryItem.children.map((item: any) => item.id))
      else queryParams.industryIds = []
      break
    case 'status':
      break
  }
  pagination.current = 1
  getJobList(assembleQueryActions())
}

function handleStaffChange(index: number, staff: any) {
  queryParams.operators[index].value = staff
  getJobList(assembleQueryActions())
}

function pageChange(pageInfo: any) {
  pagination.current = pageInfo.current
  getJobList(assembleQueryActions())
}

function goBack() {
  router.go(-1)
}

const handleCustomRow = (record: any) => {
  return {
    onClick: () => {
      router.push({
        path: `/job/${record.id}/detail`
      })
    }
  }
}

onActivated(() => {
  getJobList(assembleQueryActions())
})
</script>

<template lang="pug">
mixin page-header-section
  a-page-header(
    title="全部项目",
    sub-title="",
    @back="goBack",
    style="padding: 0 0 8px;"
  )

mixin search-filter-section
  section.job-search-filter
    a-row.search-input
      a-col(:xxl="12" :lg="18" :md="24")

      a-input-search(placeholder="职位名称搜索", enter-button, v-model:value="queryParams.name", @search="handleFilterChange('name')")
    //- .search-filter
    a-row.search-filter(:gutter="[12, 12]")
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="客户")
          a-select(
            allow-clear
            show-search
            :filter-option="false"
            v-model:value="queryParams.customerId"
            :dropdownMatchSelectWidth="false"
            :options="filter.customer"
            @search="fetchCustomerList"
            @change="(value:any) => { handleFilterChange('customer', value) }"
          )

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="项目状态")
          a-select(
            allow-clear,
            v-model:value="queryParams.status",
            :dropdownMatchSelectWidth="false",
            :options="filter.jobStatus",
            @change="(value:any) => { handleFilterChange('status', value) }"
          )
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="优先级")
          a-select(
            @change="(value) => { handleFilterChange('priority', value)} ",
            allow-clear,
            :dropdownMatchSelectWidth="false",
            v-model:value="queryParams.priority",
          )
            a-select-option(value="10") 高优先级(P0)
            a-select-option(value="5") 常规(P1)
            a-select-option(value="1") 低优先级(P2)

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="工作地")
          a-tree-select(
            @change="(value) => { handleFilterChange('areas', value)} ",
            :tree-data="filter.areas",
            :fieldNames="{ label: 'title', value: 'id' }",
            allow-clear,
            show-search
            treeNodeFilterProp="title",
            :dropdownMatchSelectWidth="false"
          )

      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="职能")
          a-select(
            allow-clear,
            show-search,
            :optionFilterProp="'label'",
            :filterOption="true"
            v-model:value="queryParams.functionId",
            :dropdownMatchSelectWidth="false",
            :options="filter.function",
            @change="(value:any) => { handleFilterChange('function', value) }"
          )

      a-col(:xxl="4" :lg="6" :md="8" :sm="12" v-for="(role, index) in filter.roles" :key="role.name")
        a-form-item(:label="role.name")
          a-select(
            :dropdownMatchSelectWidth="false",
            allow-clear,
            show-search,
            :optionFilterProp="'realName'",
            :filterOption="true"
            v-model:value="queryParams.operators[index].value",
            @change="(value:any) => { handleStaffChange(index, value) }"
            :options="role.staffList"
            :fieldNames="{ label: 'realName', value: 'id' }"
          )
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="行业")
          a-tree-select(
            @change="(value) => { handleFilterChange('industry', value)} ",
            :tree-data="filter.industry",
            :fieldNames="{ label: 'title', value: 'id' }",
            allow-clear,
            show-search
            treeNodeFilterProp="title",
            :dropdownMatchSelectWidth="false"
          )
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="工作地分类")
          a-select(
            @change="(value) => { handleFilterChange('baseAreaType', value)} ",
            allow-clear,
            :dropdownMatchSelectWidth="false",
            v-model:value="queryParams.baseAreaType",
          )
            a-select-option(value="1") 中国内地
            a-select-option(value="2") 海外 & 港澳台
      a-col(:xxl="4" :lg="6" :md="8" :sm="12")
        a-form-item(label="是否远程")
          a-select(
            @change="(value) => { handleFilterChange('remoteWork', value)} ",
            allow-clear,
            :dropdownMatchSelectWidth="false",
            v-model:value="queryParams.remoteWork",
          )
            a-select-option(value="0") 非远程
            a-select-option(value="1") 远程
      a-col(:xxl="2" :lg="3" :md="4" :sm="6")
        a-form-item(label="近30天无推荐")
          a-checkbox(
            @change="(value) => {handleFilterChange('toPM30Day', value)}",
            v-model:checked="queryParams.toPM30Day"
          )
      a-col(:xxl="2" :lg="3" :md="4" :sm="6")
        a-form-item(label="近30天无面试")
          a-checkbox(
            @change="(value) => {handleFilterChange('interviewed30Day', value)}",
            v-model:checked="queryParams.interviewed30Day"
          )

mixin position-search-list
  a-spin(:spinning="status.searchLoading")
    .position-search
      a-table(
        v-if="columnsList.length" 
        :columns="columnsList" 
        :data-source="dataList" 
        :scroll="{ x: 1200 }" 
        :pagination="pagination" 
        :customRow="handleCustomRow"
        @change="(pagination) => {pageChange(pagination)}"
        rowClassName="clickable"
        rowKey="id"
      )
        template(#bodyCell="{ column, record }")
          template(v-if="column.dataIndex === 'key_action'") 
            a 查看详情

          template(v-if="column.dataIndex === 'jobtitle'")
            .position-title 
              span {{ record.jobtitle }}
              template(v-if="record.isFromSmartDeer")
                img.smartdeer-positon(src="@/assets/sd-logo.svg" height="16")
            .customer-name {{ record.customerName }}

          template(v-if="column.dataIndex === 'key_priority'")
            JobPriority(:priority="record.priority")

          template(v-if="column.key === 'key_startDate'")
            div {{ record.positionStartDate }}
            div 距今：{{ getTimeSpan(record.positionStartDate) }}天
            
        template(#expandedRowRender="{ record }")
          .expanded
            p 项目ID: {{ record.id }}
            p BD: {{ record.bd }}
            p PM: {{ record.pm }}
            p CA: {{ record.ca }}

.position-list-page
  +page-header-section
  +search-filter-section
  +position-search-list
</template>


<style lang="scss" scoped>
.position-list-page {
  h2 {
    height: 33px;
    font-size: 24px;
    line-height: 33px;
    font-weight: 500;
    margin-bottom: 24px;
  }

  &__search {
    margin-bottom: 20px;
    width: 442px;
  }

  :deep(.clickable) {
    cursor: pointer;
  }

  .expanded {
    padding-left: 49px;
  }
}

.position-filter {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;

  &-item {
    display: flex;
    margin-bottom: 10px;

    &__title {
      font-weight: bold;
      flex: 0 0 auto;
      width: 80px;
      padding: 0px 0;
      line-height: 32px;
    }

    &__selector {
      display: flex;
      flex-wrap: wrap;
    }

    &__option {
      flex: 0 0 auto;
      padding: 0px 5px;
      margin: 0px 10px;
      line-height: 32px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #999;
        transition: all 0.2s;
      }
    }

    .active {
      color: #FF9111;
    }
  }
}

.job-search-filter {
  background-color: #fff;
  padding: 16px 16px 16px;
  border-radius: 8px;
  margin-bottom: 16px;

  .search-input {
    margin-bottom: 12px;
  }

  .search-filter {
    :deep(.ant-select-selector) {
      background-color: #f9f9f9;
      border-color: transparent;
    }

    :deep(.ant-form-item) {
      margin-bottom: 0;
    }

    :deep(.ant-select-selection-item) {
      color: #FF9111;
    }

    :deep(label) {
      background-color: #f9f9f9;
      padding-left: 8px;
    }
  }
}

.position-search {
  background-color: #fff;
  border-radius: 6px;


  .position-title {
    line-height: 20px;
    font-weight: bold;
    vertical-align: middle;
  }
  
  .customer-name {
    color: #aaa;
    font-size: 12px;
  }
  
  .smartdeer-positon {
    display: inline;
    line-height: 20px;
    vertical-align: middle;
    position: relative;
    top: -1px;
    margin-left:4px;
  }

  &-head {
    height: 74px;
    // border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
  }
}
</style>