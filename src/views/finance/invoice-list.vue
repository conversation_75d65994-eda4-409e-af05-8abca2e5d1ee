<template lang="pug">
.invoice-list-page

  a-page-header(
    title="开票列表",
    sub-title="",
    @back="() => $router.go(-1)",
    style="padding: 0 0 8px;"
  )
    template(#extra)
      a-button(type="primary" @click="() => status.showCreatePayment = true" :disabled="selectedInvoice.keys.length === 0") 创建回款单
      a-button(type="primary" @click="() => status.showCreatePaymentClaim = true") 创建回款认领单

  section.search-filter
    a-row(:gutter="[12, 12]")
      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            v-model:value="searchParams.invoiceStatus" 
            :options="options.invoiceStatus" 
            placeholder="审核状态"
            allow-clear
            @change="(value:any) => { handleFilterChange('invoiceStatus', value)}"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            allow-clear
            show-search
            :filter-option="false"
            placeholder="客户"
            v-model:value="searchParams.customerId"
            :dropdownMatchSelectWidth="false"
            :options="options.customer"
            @search="fetchCustomerList"
            @change="(value:any) => { handleFilterChange('customerId', value)}"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-range-picker(
            style="width: 100%"
            v-model:value="dataRange.applayDateRange"
            :presets="options.ranges"
            :placeholder="['申请时间','申请时间']"
            @change="() => handleDateRangeChange('applayDateRange')"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-range-picker(
            style="width: 100%"
            v-model:value="dataRange.verifyDateRange"
            :placeholder="['审核时间','审核时间']"
            :presets="options.ranges"
            @change="() => handleDateRangeChange('verifyDateRange')"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-select(
            placeholder="票据类型"
            v-model:value="searchParams.invoiceType" 
            :options="options.invoiceType"
            allow-clear
            @change="(value:any) => { handleFilterChange('invoiceType', value)}"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem
          a-select(
            style="width: 100%"
            option-label-prop="label"
            v-model:value="searchParams.financeAccountId"
            placeholder="收款账户"
            allow-clear
            @change="(value:any) => { handleFilterChange('financeAccountId', value)}"
          )
            template(v-for="(item, index) in options.entityList")
              a-select-option(
                :value="item.id" 
                :label="`${item.companyFullName} - ${item.bankFullName}`" 
                placeholder="请选择收款账户"
              )
                .companyEntity {{ item.companyFullName }}
                .compnayAccount {{ item.bankFullName }}

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="候选人姓名"
            v-model:value="searchParams.talentRealName"
            @search="(value:any) => { handleFilterChange('talentName', value) }"
          )

      a-col(:xl="6" :lg="8" :md="12" :sm="24")
        FilterItem(label="")
          a-input-search(
            placeholder="项目名称"
            v-model:value="searchParams.positionTitle"
            @search="(value:any) => { handleFilterChange('projectName', value) }"
          )

  a-table(
    :columns="invoiceListColumn" 
    :data-source="tableList" 
    :pagination="pagination"
    @change="(pagination) => {pageChange(pagination)}"
    rowClassName="clickable"
    rowKey="id"
    :loading="status.tableLoading"
    :rowSelection="{ selectedRowKeys: selectedInvoice.keys, onChange: onSelectChange, fixed: true, getCheckboxProps: getCheckboxProps }"
    :scroll="{y: winHeightStore.value - 400, x: 1000 }" 
    :customRow="(record)=>({onClick: () => { handleClickRow(record) }})"
  )
    template(#bodyCell="{ text, record, index, column }")
      template(v-if="column.key === 'offer'")
        template(v-for="(offer, index) in record.financeInvoiceDetails" :key="offer.id")
          a-space(:size="12")
            .offer-info
              label 职位: 
              span {{ offer.positionTitle }}
            .offer-info
              label 姓名: 
              span {{ offer.talentRealName }}
            .offer-info
              label 金额: 
              span {{ (offer.grandTotal / 100).toFixed(2) }} {{ record.currencyTypeKey }}
            .offer-info(v-if="record.invoiceStatus === 1")
              label 待收: 
              span {{ (offer.remainingAmount / 100).toFixed(2) }} {{ record.currencyTypeKey }}

      template(v-if="column.key === 'invoiceStatusStr'")
        a-tag(v-if="record.invoiceStatus === 0" color="blue") 待审核
        a-tag(v-if="record.invoiceStatus === 1" color="green") 通过
        a-tag(v-if="record.invoiceStatus === 2" color="red") 拒绝

      template(v-if="column.key === 'grandTotal'")
        span {{ (record.grandTotal / 100).toFixed(2) }} {{ record.currencyTypeKey }}

      template(v-if="column.key === 'actions'")
        a-space(:size="12")
          a(v-if="record.invoiceStatus === 1" @click="() => handleSelectAndCreate(record)") 创建回款
          a(@click="() => handleCreateDiscard(record)" v-if="record.invoiceStatus === 1" ) 作废
          a(@click="() => {}" v-if="record.invoiceStatus === 1" ) 转移

  
  CreateStatementModal(v-if="status.showCreatePayment" v-model:open="status.showCreatePayment" :invoiceList="selectedInvoice.list" @update="fetchInvoiceList(); status.showCreatePayment=false;")
  CreatePaymentClaimModal(v-if="status.showCreatePaymentClaim" v-model:open="status.showCreatePaymentClaim" @update="status.showCreatePaymentClaim = false")

  //- 上传票据的modal
  a-modal(
    v-model:open="status.showUpload" 
    title="上传票据"
    width="500px"
    :maskClosable="false"
    :destroyOnClose="true"
  )
    a-form(:model="uploadForm" :label-col={ style: { width: '100px', textAlign:'left' } } ref="uploadFormRef" layout="vertical")
      a-form-item(label="上传照片" name="fileList" :rules="[{type: 'array', required: true, message:'请选择票据文件'}]")
        a-upload-dragger(
          :action="API_URL.UPLOAD_FILE",
          :headers="{'Authorization': userStore.token}",
          v-model:fileList="uploadForm.fileList"
          list-type="picture"
          :maxCount="1"
          @preview="handlePreview"
        )
          .upload-dragger
            div
              InboxOutlined(style="font-size: 40px; color: #ff9111")
            div 点击或拖拽文件到此处上传

      a-form-item(
        label="票据编号"
      )
        a-input(v-model:value="uploadForm.invoiceNumber")

    template(#footer)
      a-button(@click="status.showUpload = false") 取消
      a-button(type="primary" :loading="status.uploadLoading" @click="handleUploadClick") 提交

  a-modal(
    v-model:open="status.showDiscardInvoice"
    title="作废票据"
    width="500px"
    :maskClosable="false"
    :destroyOnClose="true"
  )
    a-form(:model="uploadForm" :label-col={ style: { width: '100px', textAlign:'left' } } ref="discardFormRef" layout="vertical")
      a-form-item(
        label="候选人信息"
      )
        a-select(
          v-model:value="discardForm.processInstanceId"
          :options="discardRecord.processOptions"
          @change="handleDiscardProcessChange"
        )
      a-form-item(
        label="本次作废金额"
      )
        a-tooltip(placement="bottomLeft")
          template(#title)
            <span>当前最大可申请作废金额为：{{ discardForm.amountLimit / 100 }}</span>
          a-input(
            v-model:value="discardForm.amount"
          )
      a-form-item(
        label="作废日期"
      )
        a-date-picker(v-model:value="discardForm.discardDate" style="width:100%")
      a-form-item(
        label="作废原因"
      )
        a-textarea(v-model:value="discardForm.reason" placeholder="请如实填写该票据的作废原因" :rows="3")

    template(#footer)
      a-button(@click="status.showDiscardInvoice = false") 取消
      a-button(type="primary" :loading="status.discardLoading" @click="handleDiscardClick") 提交

</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { getInvoiceList, getInvoiceHeader, getInvoiceDetails, updateInvoice, getEntityList, createDiscardInvoice } from '@/api/finance'
import dayjs, { Dayjs } from 'dayjs'
import { message } from 'ant-design-vue'
import { UploadOutlined, InboxOutlined, ConsoleSqlOutlined } from '@ant-design/icons-vue'
import { getDateRanges } from '@/utils/util'

import FilterItem from '@/components/ui/filter-item.vue'
import CreateStatementModal from '@/components/app/create-statement-modal.vue'
import CreatePaymentClaimModal from '@/components/app/create-payment-claim-modal.vue'

import { API_URL } from '@/api/customer'
import { getCustomerList } from '@/api/customer'
import { useWinHeightStore } from '@/store/winHeight.store'
import { useUserStore } from '@/store/user.store'
import { getPaymentMethodList } from '@/api/dictionary'
import { onActivated } from 'vue'

const userStore = useUserStore()
const winHeightStore = useWinHeightStore()

const status = reactive({
  tableLoading: false,
  showVerify: false,
  uploadLoading: false,
  showUpload: false,
  showCreatePayment: false,
  showCreatePaymentClaim: false,
  discardLoading: false,
  showDiscardInvoice: false,
  transferLoading: false,
  showTransferInvoice: false
})

const options = reactive({
  ranges: getDateRanges(),
  customer: [] as any[],
  paymentMethod: [] as any[],
  entityList: [] as any[],
  invoiceStatus: [
    { label: '未审核', value: 0 },
    { label: '通过', value: 1 },
    { label: '拒绝', value: 2 },
  ],
  invoiceType: [
    { label: 'INVOICE', value: 1 },
    { label: '普通发票', value: 2 },
    { label: '增值税专用发票', value: 3 },
  ]
})

const dataRange = reactive({
  applayDateRange: [],
  verifyDateRange: [],
})

const discardRecord = reactive<any>({
  processOptions: [],
})

const searchParams = reactive({
  size: 0,
  current: 0,
  companyUserIds: [] as number[],
  customerIds: [] as number[],
  invoiceStatus: null,
  invoiceType: null,
  positionTitle: '',
  endDate: '',
  startDate: '',
  talentRealName: '',
  verifyEndDate: '',
  verifyStartDate: '',
  finaceAccountId: null
})

const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 20,
  showSizeChanger: true
})

const tableList = ref([] as any[])
const invoiceListColumn = [
  { title: "审核状态", dataIndex: "invoiceStatusStr", key: "invoiceStatusStr", fixed: true, width: 88 },
  { title: '客户', dataIndex: ['customer', 'customerFullName'], key: 'customer', width: 200 },
  { title: "发票抬头", dataIndex: "customerName", key: "customerName", width: 200 },
  { title: "开票主体 (收款账户)", dataIndex: "vendorName", key: "vendorName", width: 200 },
  { title: "开票金额", dataIndex: "grandTotal", key: "grandTotal", width: 140 },
  { title: "预计回款日期", dataIndex: "dueDate", key: "dueDate", width: 116 },
  { title: "发起人", dataIndex: "applyUserRealName", key: "applyUserRealName", width: 100 },
  { title: "OFFER 信息", key: "offer", width: 720 },
  { title: "操作", key: "actions", width: 180, fixed: 'right' },
]

const selectedInvoice = reactive({
  keys: [] as any[],
  list: [] as any[]
})

function handleSelectAndCreate(record:any) {
  selectedInvoice.keys = [record.key]
  selectedInvoice.list = [record]
  status.showCreatePayment = true
}

function handleCreateDiscard(record: any) {
  discardRecord.processOptions = []
  discardRecord.discardLimit = 0
  discardForm.jobRequirementId = 0
  discardForm.processInstanceId = ''
  discardForm.amount = 0
  discardForm.amountCent = 0
  discardForm.amountLimit = 0
  discardForm.discardDate = ''
  discardRecord.processOptions = record.financeInvoiceDetails.map((item: any, index: any) => {
    return {
      ...item,
      value: item.processInstanceId,
      label: item.positionTitle + '-' + item.talentRealName,
    }
  })
  status.showDiscardInvoice = true
}

function getCheckboxProps(record: any) {
  const verified = record.invoiceStatus == 1
  const sameCompany = selectedInvoice.list.length == 0 ? true : selectedInvoice.list[0].customerId === record.customerId
  const disabled = !(verified && sameCompany)
  return { disabled }
}

function onSelectChange(selectedKeys: any[], selectedRows: any[]) {
  selectedInvoice.keys = selectedKeys
  selectedInvoice.list = selectedRows
}

const fetchCustomerList = async (name: string = '') => {
  const res = await getCustomerList(Object.assign({}, {
    keyWord: name,
    isSearchProject: true,
    name
  }, { current: 1, size: 20 }))

  const list = res.data.customers.map((item: any) => {
    return {
      label: item.customerFullName,
      value: item.id
    }
  })
  options.customer = list
}

function handleDateRangeChange(key: string) {
  if (key === 'applayDateRange') {
    if (!dataRange.applayDateRange) {
      searchParams.startDate = ''
      searchParams.endDate = ''
    } else {
      searchParams.startDate = dayjs(dataRange.applayDateRange[0]).format('YYYY-MM-DD')
      searchParams.endDate = dayjs(dataRange.applayDateRange[1]).format('YYYY-MM-DD')
    }
  } else if (key === 'verifyDateRange') {
    if (!dataRange.verifyDateRange) {
      searchParams.verifyStartDate = ''
      searchParams.verifyEndDate = ''
    } else {
      searchParams.verifyStartDate = dayjs(dataRange.verifyDateRange[0]).format('YYYY-MM-DD')
      searchParams.verifyEndDate = dayjs(dataRange.verifyDateRange[1]).format('YYYY-MM-DD')
    }
  }
  searchParams.current = 1
  fetchInvoiceList()
}

function handleFilterChange(key: string, value: any) {
  if (key === 'invoiceStatus') {
    searchParams.invoiceStatus = value
  } else if (key === 'customerId') {
    if (value) searchParams.customerIds = [value]
    else searchParams.customerIds = []
  } else if (key === 'invoiceType') {
    searchParams.invoiceType = value
  } else if (key === 'talentName') {
    searchParams.talentRealName = value
  } else if (key === 'projectName') {
    searchParams.positionTitle = value
  } else if (key === 'financeAccountId') {
    searchParams.finaceAccountId = value
  }
  pagination.current = 1
  fetchInvoiceList()
}

const fetchInvoiceList = async () => {
  status.tableLoading = true
  try {

    selectedInvoice.keys = []
    selectedInvoice.list = []

    // 这里用户没有填写的参数，需要不传给后端。所以这里需要重新组装参数
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      invoiceStatus: searchParams.invoiceStatus === null ? undefined : searchParams.invoiceStatus,
      customerIds: searchParams.customerIds.length === 0 ? undefined : searchParams.customerIds,
      invoiceType: searchParams.invoiceType === null ? undefined : searchParams.invoiceType,
      positionTitle: searchParams.positionTitle === '' ? undefined : searchParams.positionTitle,
      finaceAccountId: searchParams.finaceAccountId === null ? undefined : searchParams.finaceAccountId,
      endDate: searchParams.endDate === '' ? undefined : searchParams.endDate,
      startDate: searchParams.startDate === '' ? undefined : searchParams.startDate,
      talentRealName: searchParams.talentRealName === '' ? undefined : searchParams.talentRealName,
      verifyEndDate: searchParams.verifyEndDate === '' ? undefined : searchParams.verifyEndDate,
      verifyStartDate: searchParams.verifyStartDate === '' ? undefined : searchParams.verifyStartDate,
    }
    const res = await getInvoiceList(params)
    const { financeInvoices, total } = res.data

    const list = financeInvoices.map((item: any) => {
      return { ...item, key: item.id }
    })
    // 清空原来选择的行
    tableList.value = list
    pagination.total = total
    status.tableLoading = false
  } catch (err) {
    status.tableLoading = false
  }
}

const pageChange = async (pageInfo: any) => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  await fetchInvoiceList()
}

const handleChangeFilter = () => {
  pagination.current = 1
  fetchInvoiceList()
}


const handleClickRow = async (record: any) => { }

const uploadFormRef = ref()
const uploadForm = reactive({
  id: 0,
  fileId: [],
  fileUrl: '',
  invoiceNumber: '',
  invoiceDate: '',
  invoiceStatus: 1,
  fileList: [] as any[]
})

const discardFormRef = ref()
const discardForm = reactive({
  invoiceId: 0,
  jobRequirementId: 0,
  processInstanceId: '',
  discardDate: '',
  amount: 0,
  amountCent: 0,
  amountLimit: 0,
  reason: ''
})

async function handleUploadClick() {
  status.uploadLoading = true
  try {
    await uploadFormRef.value.validate()
    await updateInvoice(uploadForm.id, {
      fileId: uploadForm.fileList[0].response.data.id,
      invoiceNumber: uploadForm.invoiceNumber,
    })
    await fetchInvoiceList()
    message.success('上传成功')
    status.showUpload = false
  } catch (err: any) {
    console.log(err.errorFields)
    if (err.errorFields) {
      console.log(err)
    } else {
      message.error(err.message)
    }
  }
  status.uploadLoading = false
}

async function handleDiscardClick() {
  status.discardLoading = true
  try {
    discardForm.amount = discardForm.amount * 100
    await createDiscardInvoice(discardForm.invoiceId, discardForm)
    await fetchInvoiceList()
    message.success("提交成功")
  } catch (err: any) {
    message.error(err.message)
  }
  status.discardLoading = false
  status.showDiscardInvoice = false
}

async function handleDiscardProcessChange() {
  for (let i = 0; i < discardRecord.processOptions.length; i++) {
    console.log(discardRecord.processOptions[i])
    let item = discardRecord.processOptions[i]
    discardForm.invoiceId = item.invoiceId
    discardForm.jobRequirementId = item.jobRequirementId
    discardForm.amountLimit = Math.round(item.grandTotal - item.discardTotal)
  }
  console.log()
}

async function initDict() {
  try {
    const [entityRes] = await Promise.all([
      getEntityList(),
    ])
    options.entityList = entityRes.data
  } catch (err: any) {
    message.error(err.message)
  }
}

onMounted(() => {
  initDict()
})

onActivated(()=>{
  fetchInvoiceList()
})

</script>

<style lang="sass" scoped>
.search-filter
  padding: 16px
  background-color: #fff
  border-radius: 8px
  margin-bottom: 16px
  position: sticky

.offer-info
  white-space: nowrap
  span
    margin-right: 8px
  
  label
    color: #999
</style>