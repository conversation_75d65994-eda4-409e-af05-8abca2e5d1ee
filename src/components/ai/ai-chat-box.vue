<template lang="pug">
.chat-wrapper(v-if="status.isOpen")
  .chat-header
    .chat-title {{ title }}
    .chat-close(v-on:click="status.isOpen = false") x
  .chat-body
    ChatMessageList(:messages="messages")
  .chat-footer
.chat-trigger(@click="showConversation")
  img(:src="buttonImgUrl" class="trigger-image" style="width: 50px; height: 50px;")
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRef, watch, h } from 'vue'
import ChatMessageList from '@/components/ai/chat-message-list.vue'
import ChatSender from '@/components/ai/chat-sender.vue'
import { getJobRequirementDetail, getPositionDetail } from '@/api/position'
import {
  createConversation,
  getCozeAppConfig,
  getTalentMatchingParams,
  getThirdPartyTalentMatchScore,
  runWorkflow,
  saveTopTalent,
  getPositionMapParsedAreas,
  createAiScoreQueue,
  createAiScoreQueueBatch,
  updateAiScoreQueueStatus
} from '@/api/ai'
import { Typography } from 'ant-design-vue'
import { renderHtml } from '@/components/ai/render-html'

interface ChatProps {
  "logo": string,
  "title": string,
  "buttonImgUrl": string,
  "jobId": number,
  "talentInfo": any[]
}
// console.log(123)
// debugger
const props = defineProps<ChatProps>()
const logo = toRef(props, 'logo')
const title = toRef(props, 'title')
const buttonImgUrl = toRef(props, 'buttonImgUrl')
const jobId = toRef(props, 'jobId')
const talentInfo = toRef(props, 'talentInfo')

const emit = defineEmits(['open', 'close', 'answer', 'search', 'showScore', 'nextPage'])

const jobRequirementDetail = ref<any>()
const positionDetail = ref<any>()
const areaDetail = ref<any>()

const objectType = ref('')
const objectId = ref('')
const conversationId = ref(0)

const status = reactive({
  isTyping: false,
  isOpen: false,
})

const chat = reactive<any> ({
  config: {},
  messages: []
});

const messages = ref<any>([])
const thoughtChainItems = ref<any>([])

const isProcessingTalent = ref(true)

const delay = (ms: number) => {
  return new Promise<void>((resolve) => {
    const timer: NodeJS.Timeout = setTimeout(() => {
      clearTimeout(timer);
      resolve();
    }, ms);
  });
};

const showConversation = async () => {
  status.isOpen = !status.isOpen;
}

const initChatBox = async () => {
  console.log('initChatBox')
  messages.value = []
  thoughtChainItems.value = []

  const createConversationResp = await createConversation({
    objectType: '2',
    objectId: jobId.value.toString(),
  })
  conversationId.value = createConversationResp.data

  const jobRequirementResp = await getJobRequirementDetail(props.jobId)
  jobRequirementDetail.value = jobRequirementResp.data

  const positionResp = await getPositionDetail(jobRequirementDetail.value.positionId)
  positionDetail.value = positionResp.data
  
  const areaResp = await getPositionMapParsedAreas(jobId.value)
  areaDetail.value = areaResp.data
  console.log('areaDetail', areaDetail.value)

  // Transform areaDetail into separate variables
  const areaParams: Record<string, string[]> = {}
  Object.entries(areaDetail.value).forEach(([key, value]: [string, any]) => {
    areaParams[`param${key}`] = value.map((item: any) => item.sourceId)
  })

  status.isOpen = true

  messages.value.push({
    type: 'text',
    content: '你好，请输入您需要匹配的岗位',
    placement: 'start',
    avatarStyle: {
      color: '#f56a00'
    }
  })
  await delay(600)
  messages.value.push({
    type: 'text',
    content: jobRequirementDetail.value.processName,
    placement: 'end',
    avatarStyle: {
      color: '#f56a00'
    }
  })
  await delay(200)
  messages.value.push({
    type: 'thoughtChain',
    placement: 'start',
    thoughtChainItems: thoughtChainItems.value
  })

  thoughtChainItems.value.push({
    title: '生成检索条件',
    status: 'pending',
    description: '检索条件生成中...',
    content: renderHtml('职位名称：<br>'+ jobRequirementDetail.value.processName)
  })

  const response = await runWorkflow(
    chat.config.appId,
    chat.config.paramsWorkFlowId,
    {
      conversationId: conversationId.value,
      params: {
        "jobTitle": positionDetail.value.positionTitle,
        "jobDesc": positionDetail.value.workDetail,
        "jobArea": positionDetail.value.areaStr,
        "jobAreaLiePin": areaParams.param10,
        "jobAreaBoss": areaParams.param12,
        "jobAreaLinkedIn": areaParams.param9,
      }
    }
  )
  const searchParams = JSON.parse(response.data)
  thoughtChainItems.value[thoughtChainItems.value.length - 1].status = 'success'
  thoughtChainItems.value[thoughtChainItems.value.length - 1].description = '检索条件已生成。'

  await delay(200)
  thoughtChainItems.value.push({
    title: '全网搜索人才',
    status: 'success',
    description: '检索人才完成。',
    content: renderHtml(parseParams(searchParams))
  })

  emit("search", searchParams)
}

const getTalentMatchScore = async (talent: any) => {
  if (!isProcessingTalent.value) return
  thoughtChainItems.value.push({
    title: '为人才计算匹配度',
    status: 'pending',
    description: '正在为人才打分',
    content: talent.talentInfo
  })

  await updateAiScoreQueueStatus({
    jobRequirementId: jobId.value,
    talentId: talent.site === 7 ? talent.id.toString() : 
             talent.site === 5 ? talent.bossExpectId.toString() : 
             talent.resumeId.toString(),
    site: talent.site.toString(),
    runStatus: 2 // 2 means "processing"
  })

  let resp;
  if (talent.site === 7) {
    resp = await getTalentMatchingParams(jobId.value, talent.id)
  } else {
    try {
      resp = await getThirdPartyTalentMatchScore(jobId.value, JSON.stringify(talent))
    } catch (err: any) {
      console.log(err)
      thoughtChainItems.value[thoughtChainItems.value.length - 1].status = 'success'
      thoughtChainItems.value[thoughtChainItems.value.length - 1].description = '打分完成'
      thoughtChainItems.value[thoughtChainItems.value.length - 1].content = '人才信息获取失败，请稍后再试。'
      return
    }
  }

  const params = resp.data

  const talentScore = await runWorkflow(
    chat.config.appId,
    chat.config.scoreWorkFlowId,
    {
      conversationId: conversationId.value,
      params: params
    }
  )
  const scoreObj = JSON.parse(talentScore.data)
  let formattedScore = scoreObj.score.replace('```json', '')
  formattedScore = formattedScore.replace('```', '')
  formattedScore = formattedScore.replace('json', '')
  const score = JSON.parse(scoreObj.score)
  emit("showScore", talent.site, talent.id, talent.resumeId, score)

  // Initialize recommendation with default value
  let recommendation = { output: '' }

  // Save high score talent and run workflow if score >= 75
  if (score.final >= 75) {
    window.postMessage({ type: 'add_queue', url: talent.resumeUrl}, '*');
    
    // Run workflow using chat.config
    const workflowResult = await runWorkflow(
      chat.config.appId,
      chat.config.reportWorkFlowId,
      {
        conversationId: conversationId.value,
        params: params
      }
    )
    recommendation = JSON.parse(workflowResult.data)
  }

  await updateAiScoreQueueStatus({
    jobRequirementId: jobId.value,
    talentId: talent.site === 7 ? talent.id.toString() : 
              talent.site === 5 ? talent.bossExpectId.toString() : 
              talent.resumeId.toString(),
    site: talent.site.toString(),
    score: JSON.stringify(score),
    recommendReason: recommendation.output,
    runStatus: 3 // 3 means "completed"
  })

  thoughtChainItems.value[thoughtChainItems.value.length - 1].status = 'success'
  thoughtChainItems.value[thoughtChainItems.value.length - 1].description = '打分完成'
  thoughtChainItems.value[thoughtChainItems.value.length - 1].content = renderHtml(parseScore(score))
}

onMounted(async () => {

  const appConf = await getCozeAppConfig();
  chat.config = appConf.data

  await initChatBox()
})

watch(() => props.jobId, async (value, oldValue) => {
  // Clear chat panel
  messages.value = []
  thoughtChainItems.value = []
  status.isOpen = false
  
  // Stop processing current talentInfo
  isProcessingTalent.value = false
  
  await initChatBox()
  jobRequirementDetail.value = await getJobRequirementDetail(props.jobId)
})

watch(() => props.talentInfo, async (value, oldValue) => {
  // Start processing new talentInfo
  isProcessingTalent.value = true

  const queueBatchParams = []
  for (let i = 0; i < value.length; i++) {
    queueBatchParams.push({
      jobRequirementId: jobId.value,
      talentId: value[i].site === 7 ? value[i].id.toString() : 
               value[i].site === 5 ? value[i].bossExpectId.toString() : 
               value[i].resumeId.toString(),
      site: value[i].site,
      json: JSON.stringify(value[i])
    })
  }
  await createAiScoreQueueBatch(queueBatchParams)

  for (let i = 0; i < value.length; i++) {
    await getTalentMatchScore(value[i])
  }
  // After processing all talents, trigger pagination
  emit('nextPage')
})

const parseParams = (params: any) => {
  let html = ''
  const bossParams = JSON.parse(params.boss)
  const itpParams = JSON.parse(params.itp)
  const liepinParams = JSON.parse(params.liepin)
  const linkedinParams = JSON.parse(params.linkedin)
  const maimaiParams = JSON.parse(params.maimai)
  const smartdeerParams = JSON.parse(params.smartdeer)

  html += '<h4>Boss直聘</h4>'
  html += '<p>关键词：' + bossParams.keywords + '</p>'
  html += '<h4>ITP</h4>'
  html += '<p>关键词：' + itpParams.text + '</p>'
  html += '<h4>猎聘网</h4>'
  html += '<p>关键词：' + liepinParams.keyword + '</p>'
  html += '<h4>LinkedIn</h4>'
  html += '<p>关键词：' + linkedinParams.keywords + '</p>'
  html += '<h4>脉脉</h4>'
  html += '<p>关键词：' + maimaiParams.query + '</p>'
  html += '<h4>SmartDeer</h4>'
  html += '<p>关键词：' + smartdeerParams.position_keywords.join(',') + '</p>'

  return html
}

const parseScore = (params: any) => {
  let html = ''
  for (let key in params) {
    html += '<strong>' + key + ': </strong>' + params[key] + '<br>'
  }
  return html
}

</script>

<style lang="scss" scoped>
.chat-wrapper {
  z-index: 9999;
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 400px;
  height: 580px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .chat-header {
    padding: 10px;
    border-radius: 5px;
    background-color: #ff9111;
    color: white;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chat-title {
      font-size: 16px;
    }

    .chat-close {
      cursor: pointer;
      font-size: 24px;
      line-height: 24px;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  .chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}
.chat-trigger {
  position: fixed;
  bottom: 30px;
  right: 30px;
  cursor: pointer;
  .trigger-image {
    width: 40px;
    height: 40px;
    border-radius: 50px;
  }
}
.ant-thought-chain-item-content-box {
  width: 100%;
}
</style>