<template lang="pug">
.staff-update-component
  .staff-update-form
    a-form(layout="vertical" :form="form" :model="form" ref="staffAddFormInstance")
      a-row(:gutter="[12, 0]")
        a-col(:span="24")
          a-form-item(
            label="员工姓名",
            name="realName"
            :rules="[{ required: true, message: '请填写部门名称' }]"
          )
            a-input(v-model:value="form.realName", placeholder="员工姓名")


        a-col(:span="12")

          a-form-item(
            label="员工性别",
            name="gender"
            :rules="[{ type: 'number', required: true, message: '请选择员工性别' }]"
          )
            a-radio-group(v-model:value="form.gender")
              a-radio(:value="1") 男
              a-radio(:value="2") 女
              a-radio(:value="3") 保密

        a-col(:span="12")
          a-form-item(
            label="员工类型",
            name="employeeType"
            :rules="[{ type: 'number', required: true, message: '请选择员工类型' }]"
          )
            a-radio-group(v-model:value="form.employeeType")
              a-radio(:value="0") 全职
              a-radio(:value="1") 兼职

        a-col(:span="12")
          a-form-item(
            label="手机",
            name="workMobile",
            :rules=`staff?.id ? [] : [
              { required: true, message: '请填写手机号码' },
              { type: 'string', pattern: new RegExp(/^[\\d]*$/), message: '请填入正确的手机号码' }
            ]`
          )
            a-input(
              v-model:value="form.workMobile", 
              placeholder="手机号码", 
              :maxlength="11",
              :disabled="staff?.id !== undefined"
            )

        a-col(:span="12")
          a-form-item(
            label="邮箱",
            :rules="[{ required: false, message: '', type:'email' }]"
          )
            a-input(v-model:value="form.email", placeholder="请填写员工的工作邮箱")

        a-col(:span="24")
          a-form-item(
            label="微信",
            :rules="[{ required: false, message: '' }]"
          )
            a-input(v-model:value="form.wechatNumber", placeholder="微信号用于联系候选人时，展示给候选人的微信号")
        a-col(:span="12")
          a-form-item(
            label="企业微信名片",
            name="wechatQrCode",
            :rules="[{required: false, message: ''}]"
          )
            a-upload.logo_uploader(
              name="image",
              accept="png,jpg,jpeg",
              :show-upload-list="false",
              :action="API_URL.UPLOAD_IMAGE",
              :headers="{'Authorization': userStore.token}",
              @change="handleUploadWechatQRCode"
            )
              a-avatar.avatar(
                v-if="form.wechatQrCodeUrl",
                :src="form.wechatQrCodeUrl",
                alt="avatar"
                shape="square"
                :size="140"
              )
              div.upload-area(v-else)
                LoadingOutlined(v-if="status.wechatQrCodeLoading")
                PlusOutlined(v-else)
        a-col(:span="12")
          a-form-item(
            label="员工头像",
            :rules="[{required: false, message: ''}]"
          )
            a-upload.logo_uploader(
              name="image",
              accept="png,jpg,jpeg",
              :show-upload-list="false",
              :action="API_URL.UPLOAD_IMAGE",
              :headers="{'Authorization': userStore.token}",
              @change="handleUploadFormalPhoto"
            )
              a-avatar.avatar(
                v-if="form.formalPhotoUrl",
                :src="form.formalPhotoUrl",
                alt="avatar"
                shape="square"
                :size="140"
              )
              div.upload-area(v-else)
                LoadingOutlined(v-if="status.formalPhotoLoading")
                PlusOutlined(v-else)
        a-col(:span="24" v-if="!staff?.id")
          a-form-item(
            label="部门",
            name="deptId",
            :rules="[{ type: 'number', required: true, message: '请选择部门' }]"
          )
            a-tree-select(
              v-model:value="form.deptId",
              :tree-data="departmentTreeData",
              :tree-line="true",
              placeholder="请选择员工部门"
            )

        a-col(:span="24")
          a-form-item(
            label="是否是公司管理员",
            name="isAdministrator"
            :rules="[{ type: 'number', required: true, message: '请选择是否是公司管理员' }]"
          )
            a-radio-group(v-model:value="form.isAdministrator")
              a-radio(:value="1") 是
              a-radio(:value="0") 否
        a-col(:span="24")
          a-form-item(
            label="擅长的职能",
            name="skilledFunctions"
          )
            a-select(
              placeholder="请选择顾问所擅长的职能（最多选 3 个）",
              v-model:value="form.skilledFunctions",
              :filterOption="true"
              :options="dictFunctions",
              mode="multiple",
              :maxCount="3"
            )
        a-col(:span="24")
          a-form-item(
            label="擅长的行业",
            name="skilledIndustries"
          )
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'id' }",
              placeholder="请选择顾问所擅长的行业（最多选 3 个）",
              v-model:value="form.skilledIndustries",
              :tree-data="dictIndustries",
              multiple,
              maxCount="3"
            )
        a-col(:span="24")
          a-form-item(
            label="擅长的区域/城市",
            name="skilledAreas"
          )
            a-tree-select(
              :fieldNames="{ label: 'title', value: 'id' }",
              placeholder="请选择顾问所擅长国家/城市（最多选 3 个）",
              v-model:value="form.skilledAreas",
              :tree-data="dictAreas",
              multiple
            )
        a-col(:span="24")
          a-form-item(
            label="自我介绍",
            name="introduce"
          )
            a-textarea(
              v-model:value="form.introduce",
              placeholder="请描写该员工所擅长的领域、成功案例等信息",
              :rows="4"
            )
        a-col.sub-title(:span="24")
          strong CA 评分详情
        a-col(:span="12" v-for="(caItem, index) in userScoreTypes.ca")
          a-form-item(
            :label="caItem.label"
            :name="'userCaScore_' + index"
          )
            a-rate(
              allow-half
              v-model:value="userScore.ca[caItem.value]"
              @change="(e) => handleRateChange('ca', caItem, e)"
            )
        a-col.sub-title(:span="24")
          strong PM 评分详情
        a-col(:span="12" v-for="(pmItem, index) in userScoreTypes.pm")
          a-form-item(
            :label="pmItem.label"
            :name="'userPmScore_' + index"
          )
            a-rate(
              allow-half
              v-model:value="userScore.pm[pmItem.value]"
              @change="(e) => handleRateChange('pm', pmItem, e)"
            )

  .staff-update-action
    a-space(:size="8")
      a-button(@click="handleCancelClick", type="primary", ghost) 取消
      a-button(
        type="primary",
        :loading="status.loading",
        @click="handleConfirmClick"
      ) 确定
</template>

<script lang="ts" setup>
import { reactive, ref } from '@vue/reactivity'
import { message } from 'ant-design-vue'
import { newCompanyUser, updateCompanyUser, API_URL } from '@/api/system/users'
import { getCompanyDepartment } from '@/api/system/department'
import { useUserStore } from '@/store/user.store'
import { dictionary, getAllFunctionList, getAllIndustryList } from '@/api/dictionary'
import { toRef } from 'vue'
import { areaDictToTreeData, industryDictToTreeData } from '@/utils/form-data-helper'
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue'

const userStore = useUserStore()

interface Department {
  id: number,
  companyId: number,
  parentId: number,
  deptName: string,
  userId: number,
  children?: Department[],
  title?: string,
  value?: number,
}

const emit = defineEmits(['close', 'update:success'])
const props = defineProps<{staff?: any, userScoreTypes: any}>()
const staff = toRef(props, 'staff')
const userScoreTypes = toRef(props, 'userScoreTypes')
const departmentTreeData = ref([] as any[])
const staffAddFormInstance = ref()

const dictFunctions = ref<any>([])
const dictAreas = ref<any>([])
const dictIndustries = ref<any>([])

const form = reactive({
  id: staff.value?.id,
  realName: staff.value?.realName,
  workMobile: staff.value?.workMobile,
  gender: staff.value?.gender,
  isAdministrator: staff.value?.isAdministrator,
  deptId: staff.value?.deptId,
  wechatNumber: staff.value?.wechatNumber,
  employeeType: staff.value?.employeeType,
  email: staff.value?.email,
  wechatQrCode: staff.value?.wechatQrCode,
  wechatQrCodeUrl: staff.value?.wechatQrCodeUrl,
  formalPhoto: staff.value?.formalPhoto,
  formalPhotoUrl: staff.value?.formalPhotoUrl,
  skilledFunctions: staff.value?.skilledFunctions,
  skilledIndustries: staff.value?.skilledIndustries,
  skilledAreas: staff.value?.skilledAreas,
  caUserScore: staff.value?.caUserScore,
  pmUserScore: staff.value?.pmUserScore,
  introduce: staff.value?.introduce
})

const userScore = reactive<any>({
  ca: {},
  pm: {}
})

const status = reactive({
  loading: false,
  wechatQrCodeLoading: false,
  formalPhotoLoading: false
})

const rateStarDesc = reactive([
  "非常差", "不理想", "一般", "还不错", "非常棒"
])

function handleCancelClick() {
  emit('close')
}

async function getDepartment() {
  try {
    const res = await getCompanyDepartment()
    departmentTreeData.value = _processDepartmentData(res.data)
  } catch (err: any) {
    message.error(err.message)
  }
}

getDepartment()

function _processDepartmentData(departments: Department[]) {
  const deptMap = new Map<number, Department>()

  const deptTree = new Array<Department>()

  departments.forEach((dept) => {
    dept.children = []
    dept.title = dept.deptName
    dept.value = dept.id
    deptMap.set(dept.id, dept)

    // 如果父级部门ID是0， 则表示上级无企业
    if (dept.parentId === 0) deptTree.push(dept)
  })

  departments.forEach((dept) => {
    if (dept.parentId) {
      const parent = deptMap.get(dept.parentId)
      parent?.children?.push(dept)
    }
  })

  const userStore = useUserStore()

  return [{ title: userStore.companyName, value: 0, children: deptTree }]
}

async function handleConfirmClick() {
  status.loading = true
  try {
    await staffAddFormInstance.value.validate()

    if (staff.value?.id) {
      const res = await updateCompanyUser(form)
      emit('update:success')
    } else {
      const res = await newCompanyUser([form])
      emit('update:success')
    }
  } catch (err: any) {
    if (err.errorFields) {
      message.error(err.errorFields[0].errors.join(','))
    }

    if (err.message) {
      message.error(err.message)
    }
  }
  status.loading = false
}

function handleUploadWechatQRCode(info: any) {
  if (info.file.status === 'uploading') {
    status.wechatQrCodeLoading = true
  } else if (info.file.status === 'done') {
    const fileResponse = info.file.response.data
    status.wechatQrCodeLoading = false
    form.wechatQrCodeUrl = fileResponse.fileAbsolutePath
    form.wechatQrCode = fileResponse.id
  }
}

function handleUploadFormalPhoto(info: any) {
  if (info.file.status === 'uploading') {
    status.formalPhotoLoading = true
  } else if (info.file.status === 'done') {
    const fileResponse = info.file.response.data
    status.formalPhotoLoading = false
    form.formalPhotoUrl = fileResponse.fileAbsolutePath
    form.formalPhoto = fileResponse.id
  }
}

function handleRateChange(group: any, index: any, value: any) {
  if (group === 'ca') {
    form.caUserScore[index.value] = value * 2
  }
  if (group === 'pm') {
    form.pmUserScore[index.value] = value * 2
  }
  console.log(form)
}

async function initDictionary() {
  const areaDictRes = await dictionary.getAllAreaList()
  const [areaFormData, areaMap] = areaDictToTreeData(areaDictRes.data, true)
  dictAreas.value = areaFormData

  const industryDictRes = await getAllIndustryList();
  const [industryFormData, industryMap] = industryDictToTreeData(industryDictRes.data)
  dictIndustries.value = industryFormData

  const functionDictRes = await getAllFunctionList();
  const list = functionDictRes.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id
    }
  })
  dictFunctions.value = list
}

async function initUserScore() {
  console.log(staff.value)
  if (staff.value) {
    for (let i in staff.value.caUserScore) {
      userScore.ca[i] = staff.value.caUserScore[i] / 2
    }
    for (let i in staff.value.pmUserScore) {
      userScore.pm[i] = staff.value.pmUserScore[i] / 2
    }
  }
}

initDictionary()
initUserScore()

</script>

<style lang="scss" scoped>
.staff-update-component {
  height: 100%;
  position: relative;

  .staff-update-form {
    padding: 24px;
  }

  .staff-update-action {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    bottom: 0;
    padding: 12px 24px;
    border-top: 1px solid #e8e8e8;
    text-align: right;
    background-color: #fff;
  }

  .logo_uploader {
    .upload-area {
      width: 100px;
      height: 100px;
      border: 1px dotted #999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.sub-title {
  margin-bottom: 15px;
}
</style>