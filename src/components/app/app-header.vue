<template lang="pug">
.app-header
  .app-header__logo
    img(src="@/assets/sd_logo.webp")
  nav.app-header__quick-action
    a-space(:size="0")
      a-button(type="text" @click="() => { handleAppHeaderButtonClick('create-talent', '/talent/create') }" v-if="hasPermission(PERMISSIONS.talent.children.add.code)") 新增人才
        template(#icon)
          PlusCircleFilled(:style="{ color: '#FF9111' }")
      a-button(type="text" @click="() => { handleAppHeaderButtonClick('create-talent-batch' ,'/talent/batch/create') }" v-if="hasPermission(PERMISSIONS.talent.children.add.code)") 批量导入人才
        template(#icon)
          PlusCircleFilled(:style="{ color: '#FF9111' }")
      a-button(type="text" @click="() => { handleAppHeaderButtonClick('create-job','/job/create') }" v-if="hasPermission(PERMISSIONS.project.children.add.code)") 新增项目
        template(#icon)
          PlusCircleFilled(:style="{ color: '#FF9111' }")
      a-button(type="text" @click="() => { handleAppHeaderButtonClick('create-customer', '/customer/create') }" v-if="hasPermission(PERMISSIONS.customer.children.add.code)") 新增客户
        template(#icon)
          PlusCircleFilled(:style="{ color: '#FF9111' }")

  .app-header__chat
    a-button(type="text" @click="() => { goto('/chat/unread/list') }")
      template(#icon)
        a-badge(:dot="status.unreadImMessageCount > 0")
          MessageOutlined

  .app-header__notice
    a-button(type="text" @click="handleShowNoticeList")
      template(#icon)
        a-badge(:dot="status.unreadNoticeCount > 0")
          BellOutlined()

  .app-header__account
    a-dropdown(placement="bottomRight")
      a-avatar.account-avatar(:src="`https://image.itp.smartdeer.work/images/${userStore.avatar}`") {{  userStore.name  }}
      template(#overlay)
        a-menu
          a-menu-item(:key="1" @click="()=>{goto('/account/home')}") 个人账户设置
            template(#icon)
              SettingOutlined
          a-menu-item(:key="2" @click="() => { goto('/account/password/change') }") 更换密码
            template(#icon)
              UnlockOutlined
          a-menu-item(:key="3" @click="() => { handleShowPushSetting() }") 小推设置
            template(#icon)
              SettingOutlined
          a-menu-divider
          a-menu-item(:key="0" @click="handleLogout") 退出
            template(#icon)
              LogoutOutlined
 
  a-drawer(v-model:open="status.noticeVisible" :destroyOnClose="true" :width="450" :bodyStyle="{padding: 0}" title="系统消息")
    .notice-container
      .notice-tabs
        a-tabs(v-model:activeKey="activeNoticeKey" @change="handleNoticeTypeChange")
          //- a-tab-pane(:key="null" tab="所有消息")
          a-tab-pane(:key="0" tab="流程消息")
          a-tab-pane(:key="1" tab="重要消息") 
          //- a-tab-pane(:key="2" tab="Offer喜报") 
      .notice-list
        a-spin(:spinning="status.noticeLoading")
          a-list(
            item-layout="horizontal" 
            size="small"
            :data-source="noticeList" 
            :pagination="false"
          )
            template(#renderItem="{ item }")
              a-list-item(@click="handleClickNotice(item.noticeUrl)")
                a-list-item-meta
                  template(#description)
                    div {{ item.noticeContent }}
                  template(#title)
                    .notice-item
                      .notice-title {{ item.noticeTitle }}
                      .notice-create-time {{ timeFormat(item.createTime) }}

      .notice-list-pagination
        a-pagination(
          v-model:current="noticeListQuery.current"
          v-model:pageSize="noticeListQuery.size"
          :total="noticeListQuery.total"
          :showSizeChanger="false"
          show-less-items
          @change="(pagination) => {handleNoticePageChange(pagination)}"
        )

  a-drawer(v-model:open="status.pushSettingVisible" :destroyOnClose="true" :width="450" title="小推设置")
    .push-setting-container
      a-spin(:spinning="status.pushSettingLoading")
        a-tabs(v-model:activeKey="activePushSettingKey")
          a-tab-pane(key="liepin" tab="猎聘")
            a-form(:model="pushSetting.liepin" layout="vertical")
              a-form-item(label="是否自动打开小推人选")
                a-switch(v-model:checked="pushSetting.liepin.autoOpen")
              template(v-if="pushSetting.liepin.autoOpen")
                a-divider 好友设置
                a-form-item(label="是否自动与候选人建联")
                  a-switch(v-model:checked="pushSetting.liepin.isAutoContact")
                a-form-item(label="每天访问 Profile 数量限制")
                  a-select(
                    v-model:value="pushSetting.liepin.dailyProfileLimit"
                    style="width: 100%"
                    placeholder="请选择每天访问 Profile 数量限制"
                  )
                    a-select-option(:value="50") 每天50个访问
                    a-select-option(:value="100") 每天100个访问
                    a-select-option(:value="200") 每天200个访问
                    a-select-option(:value="300") 每天300个访问
                a-form-item(label="每天发起聊天数量限制")
                  a-select(
                    v-model:value="pushSetting.liepin.dailyMessageLimit"
                    style="width: 100%"
                    placeholder="请选择每天发起聊天数量限制"
                  )
                    a-select-option(:value="10") 每天10条
                    a-select-option(:value="15") 每天15条
                    a-select-option(:value="20") 每天20条
                    a-select-option(:value="30") 每天30条
                    a-select-option(:value="50") 每天50条
                    a-select-option(:value="100") 每天100条
                  .ant-form-item-extra(style="color: #666; margin-top: 8px; font-size: 12px;") 新账号或初次使用插件从10条/天开始。
                a-form-item(label="打招呼留言内容")
                  a-textarea(v-model:value="pushSetting.liepin.message" :rows="4" placeholder="请输入打招呼留言内容")
                a-divider 聊天设置
                a-form-item(label="是否使用机器人聊天")
                  a-switch(v-model:checked="pushSetting.liepin.useBotChat")
                template(v-if="pushSetting.liepin.useBotChat")
                  a-form-item(label="生效时间")
                    a-space(direction="vertical" style="width: 100%")
                      a-radio-group(v-model:value="pushSetting.liepin.timeRangeType" button-style="solid")
                        a-radio-button(:value="'daily'") 每天
                        a-radio-button(:value="'weekly'") 自定义
                      template(v-if="pushSetting.liepin.timeRangeType === 'weekly'")
                        a-checkbox-group(v-model:value="pushSetting.liepin.selectedDays" style="margin-top: 8px")
                          a-checkbox(:value="1") 周一
                          a-checkbox(:value="2") 周二
                          a-checkbox(:value="3") 周三
                          a-checkbox(:value="4") 周四
                          a-checkbox(:value="5") 周五
                          a-checkbox(:value="6") 周六
                          a-checkbox(:value="0") 周日
                      a-space(style="width: 100%; margin-top: 8px")
                        a-select(
                          v-model:value="pushSetting.liepin.timeRange[0]"
                          style="width: 100px"
                          placeholder="开始时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                        span(style="margin: 0 4px") 至
                        a-select(
                          v-model:value="pushSetting.liepin.timeRange[1]"
                          style="width: 100px"
                          placeholder="结束时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                a-form-item(label="是否自动索要简历")
                  a-switch(v-model:checked="pushSetting.liepin.autoRequestResume")
                a-form-item(label="新消息答复频率")
                  a-select(
                    v-model:value="pushSetting.liepin.messageReplyFrequency"
                    style="width: 100%"
                    placeholder="请选择新消息答复频率"
                  )
                    a-select-option(:value="300") 5分钟内
                    a-select-option(:value="1800") 30分钟内
                    a-select-option(:value="7200") 2小时内

          a-tab-pane(key="maimai" tab="脉脉")
            a-form(:model="pushSetting.maimai" layout="vertical")
              a-form-item(label="是否自动打开小推人选")
                a-switch(v-model:checked="pushSetting.maimai.autoOpen")
              template(v-if="pushSetting.maimai.autoOpen")
                a-divider 好友设置
                a-form-item(label="是否自动与候选人建联")
                  a-switch(v-model:checked="pushSetting.maimai.isAutoContact")
                a-form-item(label="打招呼留言内容")
                  a-textarea(v-model:value="pushSetting.maimai.message" :rows="4" placeholder="请输入打招呼留言内容")
                a-divider 聊天设置
                a-form-item(label="是否使用机器人聊天")
                  a-switch(v-model:checked="pushSetting.maimai.useBotChat")
                template(v-if="pushSetting.maimai.useBotChat")
                  a-form-item(label="生效时间")
                    a-space(direction="vertical" style="width: 100%")
                      a-radio-group(v-model:value="pushSetting.maimai.timeRangeType" button-style="solid")
                        a-radio-button(:value="'daily'") 每天
                        a-radio-button(:value="'weekly'") 自定义
                      template(v-if="pushSetting.maimai.timeRangeType === 'weekly'")
                        a-checkbox-group(v-model:value="pushSetting.maimai.selectedDays" style="margin-top: 8px")
                          a-checkbox(:value="1") 周一
                          a-checkbox(:value="2") 周二
                          a-checkbox(:value="3") 周三
                          a-checkbox(:value="4") 周四
                          a-checkbox(:value="5") 周五
                          a-checkbox(:value="6") 周六
                          a-checkbox(:value="0") 周日
                      a-space(style="width: 100%; margin-top: 8px")
                        a-select(
                          v-model:value="pushSetting.maimai.timeRange[0]"
                          style="width: 100px"
                          placeholder="开始时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                        span(style="margin: 0 4px") 至
                        a-select(
                          v-model:value="pushSetting.maimai.timeRange[1]"
                          style="width: 100px"
                          placeholder="结束时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                a-form-item(label="是否自动索要简历")
                  a-switch(v-model:checked="pushSetting.maimai.autoRequestResume")
                a-form-item(label="新消息答复频率")
                  a-select(
                    v-model:value="pushSetting.maimai.messageReplyFrequency"
                    style="width: 100%"
                    placeholder="请选择新消息答复频率"
                  )
                    a-select-option(:value="300") 5分钟内
                    a-select-option(:value="1800") 30分钟内
                    a-select-option(:value="7200") 2小时内

          a-tab-pane(key="boss" tab="Boss直聘")
            a-form(:model="pushSetting.boss" layout="vertical")
              a-form-item(label="是否自动打开小推人选")
                a-switch(v-model:checked="pushSetting.boss.autoOpen")
              template(v-if="pushSetting.boss.autoOpen")
                a-divider 好友设置
                a-form-item(label="是否自动与候选人建联")
                  a-switch(v-model:checked="pushSetting.boss.isAutoContact")
                a-form-item(label="打招呼留言内容")
                  a-textarea(v-model:value="pushSetting.boss.message" :rows="4" placeholder="请输入打招呼留言内容")
                a-divider 聊天设置
                a-form-item(label="是否使用机器人聊天")
                  a-switch(v-model:checked="pushSetting.boss.useBotChat")
                template(v-if="pushSetting.boss.useBotChat")
                  a-form-item(label="生效时间")
                    a-space(direction="vertical" style="width: 100%")
                      a-radio-group(v-model:value="pushSetting.boss.timeRangeType" button-style="solid")
                        a-radio-button(:value="'daily'") 每天
                        a-radio-button(:value="'weekly'") 自定义
                      template(v-if="pushSetting.boss.timeRangeType === 'weekly'")
                        a-checkbox-group(v-model:value="pushSetting.boss.selectedDays" style="margin-top: 8px")
                          a-checkbox(:value="1") 周一
                          a-checkbox(:value="2") 周二
                          a-checkbox(:value="3") 周三
                          a-checkbox(:value="4") 周四
                          a-checkbox(:value="5") 周五
                          a-checkbox(:value="6") 周六
                          a-checkbox(:value="0") 周日
                      a-space(style="width: 100%; margin-top: 8px")
                        a-select(
                          v-model:value="pushSetting.boss.timeRange[0]"
                          style="width: 100px"
                          placeholder="开始时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                        span(style="margin: 0 4px") 至
                        a-select(
                          v-model:value="pushSetting.boss.timeRange[1]"
                          style="width: 100px"
                          placeholder="结束时间"
                        )
                          a-select-option(v-for="time in 48" :key="time-1" :value="`${String(Math.floor((time-1)/2)).padStart(2, '0')}:${(time-1)%2 === 0 ? '00' : '30'}`") {{ String(Math.floor((time-1)/2)).padStart(2, '0') }}:{{ (time-1)%2 === 0 ? '00' : '30' }}
                a-form-item(label="是否自动索要简历")
                  a-switch(v-model:checked="pushSetting.boss.autoRequestResume")
                a-form-item(label="新消息答复频率")
                  a-select(
                    v-model:value="pushSetting.boss.messageReplyFrequency"
                    style="width: 100%"
                    placeholder="请选择新消息答复频率"
                  )
                    a-select-option(:value="300") 5分钟内
                    a-select-option(:value="1800") 30分钟内
                    a-select-option(:value="7200") 2小时内
                
          a-tab-pane(key="linkedin" tab="LinkedIn")
            a-form(:model="pushSetting.linkedin" layout="vertical")
              a-form-item(label="是否自动打开小推人选")
                a-switch(v-model:checked="pushSetting.linkedin.autoOpen")
              a-form-item(label="生效国家/地区")
                a-tree-select(
                  multiple
                  v-model:value="pushSetting.linkedin.countries"
                  @change="handleLinkedInCountryChange"
                  placeholder="请选择国家/地区"
                  style="width: 100%"
                  :loading="status.countryLoading"
                  :tree-data="countryOptions"
                  :fieldNames="{ label: 'title', value: 'id' }"
                  allow-clear
                  show-search
                  :dropdownMatchSelectWidth="false"
                )
              template(v-if="pushSetting.linkedin.autoOpen")
                a-divider 好友设置
                a-form-item(label="是否自动与候选人建联")
                  a-switch(v-model:checked="pushSetting.linkedin.isAutoContact")
                a-form-item(label="打招呼留言内容")
                  a-textarea(v-model:value="pushSetting.linkedin.message" :rows="4" placeholder="请输入打招呼留言内容")
                a-divider 每日限制
                a-form-item(label="每天访问 Profile 数量限制")
                  a-select(
                    v-model:value="pushSetting.linkedin.dailyProfileLimit"
                    style="width: 100%"
                    placeholder="请选择每天访问 Profile 数量限制"
                  )
                    a-select-option(:value="50") 每天50个访问（最安全）
                    a-select-option(:value="100") 每天100个访问（免费会员）
                    a-select-option(:value="250") 每天250个访问（商务会员）
                    a-select-option(:value="600") 每天600个访问（招聘会员）
                a-form-item(label="每天发送好友申请数量限制")
                  a-select(
                    v-model:value="pushSetting.linkedin.dailyConnectionLimit"
                    style="width: 100%"
                    placeholder="请选择每天发送好友申请数量限制"
                  )
                    a-select-option(:value="3") 每天3个
                    a-select-option(:value="5") 每天5个
                    a-select-option(:value="10") 每天10个
                    a-select-option(:value="15") 每天15个
                    a-select-option(:value="20") 每天20个
                    a-select-option(:value="25") 每天25个
                  .ant-form-item-extra(style="color: #ff4d4f; margin-top: 8px; font-size: 12px;") 每天建立连接的候选人不要超过您 LinkedIn 账号已有联系人总数的 3%，否则会面临封号风险！
                a-form-item(label="每天发送消息数限制")
                  a-select(
                    v-model:value="pushSetting.linkedin.dailyMessageLimit"
                    style="width: 100%"
                    placeholder="请选择每天发送消息数限制"
                  )
                    a-select-option(:value="3") 每天3条
                    a-select-option(:value="5") 每天5条
                    a-select-option(:value="10") 每天10条
                    a-select-option(:value="15") 每天15条
                    a-select-option(:value="20") 每天20条
                    a-select-option(:value="25") 每天25条
                  .ant-form-item-extra(style="color: #666; margin-top: 8px; font-size: 12px;") 新账号或初次使用插件从3条/天开始，逐周增加5条/天
                a-divider 速度限制
                a-form-item(label="打开 Profile 的速度")
                  a-select(
                    v-model:value="pushSetting.linkedin.profileOpenSpeed"
                    style="width: 100%"
                    placeholder="请选择打开 Profile 的速度"
                  )
                    a-select-option(:value="10") 每小时10个以下
                    a-select-option(:value="20") 每小时20个以下
                    a-select-option(:value="30") 每小时30个以下
                    a-select-option(:value="50") 每小时50个以下
                a-form-item(label="扫描 Profile 的速度")
                  a-select(
                    v-model:value="pushSetting.linkedin.profileScanSpeed"
                    style="width: 100%"
                    placeholder="请选择扫描 Profile 的速度"
                  )
                    a-select-option(:value="60") 每分钟1个页面
                    a-select-option(:value="20") 每分钟3个页面
                    a-select-option(:value="12") 每分钟5个页面
                    a-select-option(:value="7.5") 每分钟8个页面
                  .ant-form-item-extra(style="color: #666; margin-top: 8px; font-size: 12px;") 指的是页面的平均停留时间，LinkedIn 会根据浏览 Profile 的时间来判断是否安全访问
                a-form-item(label="Profile 访问限制")
                  a-space(style="width: 100%" direction="vertical")
                    a-radio-group(v-model:value="pushSetting.linkedin.profilePauseEnabled" button-style="solid")
                      a-radio-button(:value="true") 启用限制
                      a-radio-button(:value="false") 不限制
                    a-space(v-if="pushSetting.linkedin.profilePauseEnabled" style="width: 100%" align="center")
                      span 每访问
                      a-input-number(
                        v-model:value="pushSetting.linkedin.profilePauseCount"
                        :min="1"
                        size="small"
                        style="width: 80px"
                      )
                      span 个 Profile 后暂停
                      a-input-number(
                        v-model:value="pushSetting.linkedin.profilePauseMinutes"
                        :min="1"
                        size="small"
                        style="width: 80px"
                      )
                      span 分钟
        .push-setting-footer
          a-button(type="primary" @click="handleSavePushSetting(activePushSettingKey)" :loading="status.pushSettingLoading") 保存

  //- 临时关闭喜报
  //- template(v-if="status.showCelerbrate")
    //- CelebrateNotice(:notice="celerbrateNotice" @finish="handleCelerbrateFinish")
</template>

<script lang="ts" setup>
import { reactive, ref, h, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useUserStore } from '@/store/user.store'
import { BellOutlined, MessageOutlined, LogoutOutlined, SettingOutlined, PlusCircleFilled, UnlockOutlined, InfoCircleFilled } from '@ant-design/icons-vue'
import { onBeforeRouteLeave, useRouter } from 'vue-router'
import { message, notification, Button } from 'ant-design-vue'
import { getNoticeList, getNoticeUnread, setNoticeRead } from '@/api/notice'
import dayjs from "dayjs"
import { getUnReadMessageByImUserId } from '@/api/chat'
import CelebrateNotice from '@/components/app/celebrate-notice.vue'
import { PERMISSIONS, hasPermission } from "@/utils/permission"
import tracker from '@/utils/tracker'
import { getPushSetting, savePushSetting } from '@/api/ai'
import { getAllAiConfigAreaList } from '@/api/ai'
import { areaDictToTreeData } from '@/utils/form-data-helper'
const userStore = useUserStore()
const router = useRouter()
const unreadCelerbrateList: any[] = []
const celerbrateNotice = ref<any>()
const noticeList = ref([] as any[])
const countryOptions = ref<any>([] as any[])
const activeNoticeKey = ref(0)
const activePushSettingKey = ref('liepin')

const status = reactive({
  noticeLoading: false,
  noticeVisible: false,
  pushSettingLoading: false,
  unreadImMessageCount: 0,
  unreadNoticeCount: 0,
  showNoticeList: false,
  showCelerbrate: false,
  pushSettingVisible: false,
  countryLoading: false,
})

const noticeListQuery = reactive({
  current: 1,
  total: 0,
  size: 10,
  showSizeChanger: false,
  type: 0,
})

const pushSetting = reactive({
  liepin: {
    autoOpen: false,
    isAutoContact: false,
    message: '',
    dailyProfileLimit: 0,
    dailyMessageLimit: 0,
    autoReplyDelay: 30,
    messageScanInterval: 30,
    useBotChat: false,
    autoRequestResume: false,
    messageReplyFrequency: 300,
    timeRangeType: 'daily',
    selectedDays: [],
    timeRange: [null, null]
  },
  maimai: {
    autoOpen: false,
    isAutoContact: false,
    message: '',
    useBotChat: false,
    autoRequestResume: false,
    messageReplyFrequency: 300,
    timeRangeType: 'daily',
    selectedDays: [],
    timeRange: [null, null]
  },
  boss: {
    autoOpen: false,
    isAutoContact: false,
    message: '',
    useBotChat: false,
    autoRequestResume: false,
    messageReplyFrequency: 300,
    timeRangeType: 'daily',
    selectedDays: [],
    timeRange: [null, null]
  },
  linkedin: {
    autoOpen: false,
    isAutoContact: false,
    message: '',
    countries: [],
    countryIds: [],
    dailyProfileLimit: 0,
    dailyConnectionLimit: 0,
    dailyMessageLimit: 0,
    profileOpenSpeed: 1,
    profileScanSpeed: 1,
    profilePauseEnabled: true,
    profilePauseCount: 20,
    profilePauseMinutes: 3
  }
})

function handleLogout() {
  userStore.logout()
}

function goto(path: string) {
  if (path) router.push(path)
}

function handleAppHeaderButtonClick(key:string, path:string) {
  tracker.click('app-header-btn-click', { key })
  goto(path)
}

const fetchNoticeList = async () => {
  status.noticeLoading = true
  try {
    const res = await getNoticeList(noticeListQuery)
    const { total, companyNotices } = res.data
    noticeListQuery.total = total
    noticeList.value = companyNotices
  } catch (err: any) {
    message.error(err.message)
  }
  status.noticeLoading = false
}

function handleNoticeTypeChange() {
  noticeListQuery.current = 1
  noticeListQuery.type = activeNoticeKey.value
  fetchNoticeList()
}

const handleNoticePageChange = async (current: number) => {
  noticeListQuery.current = current
  fetchNoticeList()
}

const fetchUnreadNotice = async () => {
  try {
    const res = await getNoticeUnread()
    status.unreadNoticeCount = res.data.length
    res.data.forEach((item: any) => {
      // 临时关闭OFFER喜报
      if (item.noticeType === 2) {
        unreadCelerbrateList.push(item)
      } else {
        openNotification(item)
      }
    })

  } catch (err: any) {
    console.error('err _getNoticeUnread', err.message)
  }
}

// const FROM_USER = ['production', 'test'].includes(import.meta.env.VITE_VUE_APP_BUILD_ENV)
//   ? 'smart:0:smartdeer'
//   : 'test:0:smartdeer'
const FROM_USER = 'smart:0:smartdeer'
async function fetchUnreadImMessage() {
  try {
    const res = await getUnReadMessageByImUserId(FROM_USER, { current: 1, pageSize: 10 })
    const { total } = res.data
    status.unreadImMessageCount = total
  } catch (err: any) {
    console.log(err.message)
  }
}

function handleShowNoticeList() {
  status.noticeVisible = true
  noticeListQuery.current = 1
  fetchNoticeList()
}

async function fetchCountryOptions() {
  const res = await getAllAiConfigAreaList()
  const [areaFormData, areaMap] = areaDictToTreeData(res.data, true)
  countryOptions.value = areaFormData
}
const openNotification = (notice: { noticeTitle: string, noticeContent: string, id: number, noticeUrl: string, noticeType:number }) => {
  const { id, noticeTitle, noticeContent, noticeUrl, noticeType } = notice

  // 根据noticeType判断是否显示icon
  const icon = noticeType === 1 
    ? () => h(InfoCircleFilled, { style: { color: '#FF9111', fontSize: 60 } }) 
    : undefined
  const style = noticeType === 1 ? { backgroundColor: '#FDF4EB' } : undefined

  notification.info({
    key: id + '',
    message: noticeTitle || '消息通知',
    description: noticeContent,
    duration: null,
    style: style,
    icon: icon,
    btn: () =>
      h(
        Button,
        {
          type: 'primary',
          size: 'small',
          onClick: () => {
            notification.close(id + '')
            setNoticeRead(id)
          },
        },
        { default: () => '已读' },
      ),
    onClick() {
      if (noticeUrl) {
        notification.close(id + '')
        goto(noticeUrl)
      }
    },
    onClose() {
      setNoticeRead(id)
    }
  })
}

const handleClickNotice = (path: string) => {
  const rand = (Date.now() - Math.ceil(Math.random() * 100000)).toString(32);
  const url = path.includes('?') ? `${path}&_t=${rand}` : `${path}?_t=${rand}`
  router.push(url)
}

let noticePollingTimer: any = null
let imMessagePollingTimer: any = null
let celerbratePollingTimer: any = null

function handleCelerbrateFinish(notice: any) {
  status.showCelerbrate = false
  setNoticeRead(notice.id)
}

function startCelerbratePolling() {
  return setInterval(() => {
    if (unreadCelerbrateList.length > 0) {
      const notice = unreadCelerbrateList.shift()
      localStorage.setItem('itp:celebrate', JSON.stringify(notice))
      celerbrateNotice.value = notice
      status.showCelerbrate = true
    }
  }, 8000)
}

function startNoticePolling() {
  clearInterval(noticePollingTimer)
  fetchUnreadNotice()
  return setInterval(() => { fetchUnreadNotice() }, 10000)
}

function startImMessagePolling() {
  fetchUnreadImMessage()
  return setInterval(() => { fetchUnreadImMessage() }, 10000)
}

onMounted(() => {
  initCelerbrateMessageListener()
  noticePollingTimer = startNoticePolling()
  imMessagePollingTimer = startImMessagePolling()
  celerbratePollingTimer = startCelerbratePolling()
  fetchCountryOptions()
})
// 清除定时器
onBeforeUnmount(() => {
  clearInterval(noticePollingTimer)
  clearInterval(imMessagePollingTimer)
  clearInterval(celerbratePollingTimer)
})

const timeFormat = (val: number) => {
  return dayjs(val).format('YYYY.MM.DD HH:mm')
}



function initCelerbrateMessageListener() {
  window.addEventListener('storage', (event:any) => {
    if (event.key === 'itp:celebrate') {
      celerbrateNotice.value = JSON.parse(event.newValue)
      status.showCelerbrate = true
    }
  })
}

function handleShowPushSetting() {
  status.pushSettingVisible = true
  fetchPushSetting()
}

function handleLinkedInCountryChange(value: any) {
  pushSetting.linkedin.countries = value
  pushSetting.linkedin.countryIds = value
}

async function fetchPushSetting() {
  status.pushSettingLoading = true
  try {
    const res = await getPushSetting()
    const { data } = res
    
    // Default values if data is empty
    const defaultSetting = {
      autoOpen: 0,
      isAutoContact: 0,
      contactContent: '',
      dailyProfileLimit: 0,
      dailyConnectionLimit: 0,
      dailyMessageLimit: 0,
      profileOpenSpeed: 1,
      profileScanSpeed: 1,
      profilePauseEnabled: true,
      profilePauseCount: 20,
      profilePauseMinutes: 3,
      useBotChat: false,
      autoRequestResume: false,
      messageReplyFrequency: 300,
      timeRangeType: 'daily',
      selectedDays: [],
      timeRange: null
    }
    
    // Map API data to UI state with fallbacks for null/empty values
    const liepinData = data && data.liepin ? data.liepin : defaultSetting
    const maimaiData = data && data.maimai ? data.maimai : defaultSetting
    const bossData = data && data.boss ? data.boss : defaultSetting
    const linkedInData = data && data.linkedIn ? data.linkedIn : defaultSetting
    
    // Set UI state with proper fallbacks
    pushSetting.liepin.autoOpen = (liepinData.autoOpen === 1)
    pushSetting.liepin.isAutoContact = (liepinData.isAutoContact === 1)
    pushSetting.liepin.message = liepinData.contactContent || ''
    pushSetting.liepin.dailyProfileLimit = liepinData.dailyProfileLimit || 0
    pushSetting.liepin.dailyMessageLimit = liepinData.dailyMessageLimit || 0
    pushSetting.liepin.useBotChat = liepinData.useBotChat || false
    pushSetting.liepin.autoRequestResume = liepinData.autoRequestResume || false
    pushSetting.liepin.messageReplyFrequency = liepinData.messageReplyFrequency || 300
    pushSetting.liepin.timeRangeType = liepinData.timeRangeType || 'daily'
    pushSetting.liepin.selectedDays = liepinData.selectedDays || []
    pushSetting.liepin.timeRange = liepinData.timeRange || [null, null]
    
    pushSetting.maimai.autoOpen = (maimaiData.autoOpen === 1)
    pushSetting.maimai.isAutoContact = (maimaiData.isAutoContact === 1)
    pushSetting.maimai.message = maimaiData.contactContent || ''
    pushSetting.maimai.useBotChat = maimaiData.useBotChat || false
    pushSetting.maimai.autoRequestResume = maimaiData.autoRequestResume || false
    pushSetting.maimai.messageReplyFrequency = maimaiData.messageReplyFrequency || 300
    pushSetting.maimai.timeRangeType = maimaiData.timeRangeType || 'daily'
    pushSetting.maimai.selectedDays = maimaiData.selectedDays || []
    pushSetting.maimai.timeRange = maimaiData.timeRange || [null, null]
    
    pushSetting.boss.autoOpen = (bossData.autoOpen === 1)
    pushSetting.boss.isAutoContact = (bossData.isAutoContact === 1)
    pushSetting.boss.message = bossData.contactContent || ''
    pushSetting.boss.useBotChat = bossData.useBotChat || false
    pushSetting.boss.autoRequestResume = bossData.autoRequestResume || false
    pushSetting.boss.messageReplyFrequency = bossData.messageReplyFrequency || 300
    pushSetting.boss.timeRangeType = bossData.timeRangeType || 'daily'
    pushSetting.boss.selectedDays = bossData.selectedDays || []
    pushSetting.boss.timeRange = bossData.timeRange || [null, null]
    
    pushSetting.linkedin.autoOpen = (linkedInData.autoOpen === 1)
    pushSetting.linkedin.isAutoContact = (linkedInData.isAutoContact === 1)
    pushSetting.linkedin.message = linkedInData.contactContent || ''
    pushSetting.linkedin.countries = linkedInData.countries || []
    pushSetting.linkedin.dailyProfileLimit = linkedInData.dailyProfileLimit || 0
    pushSetting.linkedin.dailyConnectionLimit = linkedInData.dailyConnectionLimit || 0
    pushSetting.linkedin.dailyMessageLimit = linkedInData.dailyMessageLimit || 0
    pushSetting.linkedin.profileOpenSpeed = linkedInData.profileOpenSpeed || 1
    pushSetting.linkedin.profileScanSpeed = linkedInData.profileScanSpeed || 1
    pushSetting.linkedin.profilePauseEnabled = linkedInData.profilePauseEnabled ?? true
    pushSetting.linkedin.profilePauseCount = linkedInData.profilePauseCount || 20
    pushSetting.linkedin.profilePauseMinutes = linkedInData.profilePauseMinutes || 3
  } catch (err: any) {
    // Reset to defaults on error
    pushSetting.liepin.autoOpen = false
    pushSetting.liepin.isAutoContact = false
    pushSetting.liepin.message = ''
    pushSetting.liepin.dailyProfileLimit = 0
    pushSetting.liepin.dailyMessageLimit = 0
    pushSetting.liepin.useBotChat = false
    pushSetting.liepin.autoRequestResume = false
    pushSetting.liepin.messageReplyFrequency = 300
    pushSetting.liepin.timeRangeType = 'daily'
    pushSetting.liepin.selectedDays = []
    pushSetting.liepin.timeRange = [null, null]
    pushSetting.maimai.autoOpen = false
    pushSetting.maimai.isAutoContact = false
    pushSetting.maimai.message = ''
    pushSetting.maimai.useBotChat = false
    pushSetting.maimai.autoRequestResume = false
    pushSetting.maimai.messageReplyFrequency = 300
    pushSetting.maimai.timeRangeType = 'daily'
    pushSetting.maimai.selectedDays = []
    pushSetting.maimai.timeRange = [null, null]
    pushSetting.boss.autoOpen = false
    pushSetting.boss.isAutoContact = false
    pushSetting.boss.message = ''
    pushSetting.boss.useBotChat = false
    pushSetting.boss.autoRequestResume = false
    pushSetting.boss.messageReplyFrequency = 300
    pushSetting.boss.timeRangeType = 'daily'
    pushSetting.boss.selectedDays = []
    pushSetting.boss.timeRange = [null, null]
    pushSetting.linkedin.autoOpen = false
    pushSetting.linkedin.isAutoContact = false
    pushSetting.linkedin.message = ''
    pushSetting.linkedin.countries = []
    pushSetting.linkedin.dailyProfileLimit = 0
    pushSetting.linkedin.dailyConnectionLimit = 0
    pushSetting.linkedin.dailyMessageLimit = 0
    pushSetting.linkedin.profileOpenSpeed = 1
    pushSetting.linkedin.profileScanSpeed = 1
    pushSetting.linkedin.profilePauseEnabled = true
    pushSetting.linkedin.profilePauseCount = 20
    pushSetting.linkedin.profilePauseMinutes = 3
    
    message.error(err.message || '获取小推设置失败')
  } finally {
    status.pushSettingLoading = false
  }
}

async function handleSavePushSetting(type: 'liepin' | 'maimai' | 'boss' | 'linkedin') {
  status.pushSettingLoading = true
  
  try {
    // Convert UI state to API format
    const params = {
      liepinConfig: {
        autoOpen: pushSetting.liepin.autoOpen ? 1 : 0,
        isAutoContact: pushSetting.liepin.isAutoContact ? 1 : 0,
        contactContent: pushSetting.liepin.message,
        dailyProfileLimit: pushSetting.liepin.dailyProfileLimit,
        dailyMessageLimit: pushSetting.liepin.dailyMessageLimit,
        useBotChat: pushSetting.liepin.useBotChat,
        autoRequestResume: pushSetting.liepin.autoRequestResume,
        messageReplyFrequency: pushSetting.liepin.messageReplyFrequency,
        timeRangeType: pushSetting.liepin.timeRangeType,
        selectedDays: pushSetting.liepin.selectedDays,
        timeRange: pushSetting.liepin.timeRange
      },
      maimaiConfig: {
        autoOpen: pushSetting.maimai.autoOpen ? 1 : 0,
        isAutoContact: pushSetting.maimai.isAutoContact ? 1 : 0,
        contactContent: pushSetting.maimai.message,
        useBotChat: pushSetting.maimai.useBotChat,
        autoRequestResume: pushSetting.maimai.autoRequestResume,
        messageReplyFrequency: pushSetting.maimai.messageReplyFrequency,
        timeRangeType: pushSetting.maimai.timeRangeType,
        selectedDays: pushSetting.maimai.selectedDays,
        timeRange: pushSetting.maimai.timeRange
      },
      bossConfig: {
        autoOpen: pushSetting.boss.autoOpen ? 1 : 0,
        isAutoContact: pushSetting.boss.isAutoContact ? 1 : 0,
        contactContent: pushSetting.boss.message,
        useBotChat: pushSetting.boss.useBotChat,
        autoRequestResume: pushSetting.boss.autoRequestResume,
        messageReplyFrequency: pushSetting.boss.messageReplyFrequency,
        timeRangeType: pushSetting.boss.timeRangeType,
        selectedDays: pushSetting.boss.selectedDays,
        timeRange: pushSetting.boss.timeRange
      },
      linkedInConfig: {
        autoOpen: pushSetting.linkedin.autoOpen ? 1 : 0,
        isAutoContact: pushSetting.linkedin.isAutoContact ? 1 : 0,
        contactContent: pushSetting.linkedin.message,
        countries: pushSetting.linkedin.countries,
        dailyProfileLimit: pushSetting.linkedin.dailyProfileLimit,
        dailyConnectionLimit: pushSetting.linkedin.dailyConnectionLimit,
        dailyMessageLimit: pushSetting.linkedin.dailyMessageLimit,
        profileOpenSpeed: pushSetting.linkedin.profileOpenSpeed,
        profileScanSpeed: pushSetting.linkedin.profileScanSpeed,
        profilePauseEnabled: pushSetting.linkedin.profilePauseEnabled,
        profilePauseCount: pushSetting.linkedin.profilePauseCount,
        profilePauseMinutes: pushSetting.linkedin.profilePauseMinutes
      }
    }
    
    await savePushSetting(params)
    const platformNames = {
      liepin: '猎聘',
      maimai: '脉脉',
      boss: 'Boss直聘',
      linkedin: 'LinkedIn'
    }
    message.success(`${platformNames[type]}小推设置保存成功`)
  } catch (err: any) {
    message.error(err.message || '保存小推设置失败')
  } finally {
    status.pushSettingLoading = false
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  height: 100%;
  align-items: center;

  &__logo {
    flex: 0 0 auto;
    width: 200px;

    img {
      width: 140px;
      margin: 0 auto;
    }
  }

  &__notice {
    padding: 0 5px;
    flex: 0 0 auto;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  &__quick-action {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
  }

  &__account {
    padding: 0 20px 0 5px;
    flex: 0 0 auto;
    display: flex;
    align-items: center;

    .account-avatar {
      cursor: pointer;
    }

    .person-info {
      display: flex;
      align-items: center;
    }
  }
}

.notice-container {
  .notice-tabs {
    position: sticky;
    top: 0;
    padding: 0 16px;
    background-color: #fff;
    z-index: 1;
  }

  .notice-list{
    .notice-item {
      cursor: pointer;
      transition: all .3s;
      &:hover {
        color: #ff9111;
        transition: all .3s;
      }
    }
  }

  .notice-list-pagination {
    position: sticky;
    bottom: 0;
    text-align: center;
    background-color: #fff;
    padding: 16px;
  }
}

.push-setting-container {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  
  :deep(.ant-form-item-control-input) {
    width: 100%;
  }
  
  .push-setting-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    text-align: right;
    z-index: 1000;
  }

  :deep(.ant-tabs-content) {
    padding-bottom: 72px;
  }
}
</style>
