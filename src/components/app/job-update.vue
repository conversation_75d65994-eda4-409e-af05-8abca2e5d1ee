<template lang=pug>
.job-update-component
  .job-update-form
    a-spin(:spinning="status.loading")
      a-form(labelAlign="left" ref="formInstance" :model="form" layout="vertical")
        a-form-item(
          name="status"
          label="项目状态"
        )
          a-select(
            placeholder="项目状态",
            v-model:value="form.status",
            :options="dict.jobStatus",
          )

        a-form-item(
          name="priority"
          label="优先级"
        )
          a-select(v-model:value="form.priority")
            a-select-option(:value="10") 最高优先级(P0)
            a-select-option(:value="5") 常规(P1)
            a-select-option(:value="1") 低优先级(P2)

        a-form-item(
          label="BD"
          name="bd"
          :rules="[{ type: 'array', required: true, message: '请至少指定一名BD', trigger: ['change'] }]"
        )
          a-select(
            placeholder="请指定BD",
            v-model:value="form.bd",
            mode="multiple",
            :options="dict.BDUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="PM"
          name="pm"
          :rules="[{ type: 'array', required: true, message: '请至少指定一名PM', trigger: ['change'] }]" 
        )
          a-select(
            placeholder="请指定PM",
            v-model:value="form.pm",
            mode="multiple",
            :options="dict.PMUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="CA"
          name="ca"
          :rules="[{type: 'array', required: true, message: '请至少指定一名CA', trigger: ['change'] }]"
        )
          a-select(
            placeholder="请指定CA",
            v-model:value="form.ca",
            mode="multiple",
            :options="dict.CAUsers",
            show-search
            :filter-option="filterOption"
          )

        a-form-item(
          label="是否可发布到三方平台"
          name="canPublishToPlatform"
          :rules="[{ required: true, type:'boolean', message: '请选择是否可发布到三方平台' }]"
        )
          a-radio-group(
            v-model:value="form.canPublishToPlatform",
            :options="[{label: '是', value: true},{label: '否', value: false}]",
          )

  .job-update-action
    a-space(:size="12")
      a-button(@click="()=>$emit('close')" ) 取消
      a-button(@click="updateJob" type="primary") 保存
</template>


<script lang=ts setup>
import { getJobStatus } from '@/api/dictionary'
import { getJobRequirementDetail } from '@/api/position'
import { message } from 'ant-design-vue'
import { onMounted, reactive, toRef, ref } from 'vue'
import { getCompanyOnboardUsers } from '@/api/system/roles'
import { updateJobRequirement } from '@/api/job'

interface JobDetail {
  bd: number[],
  pm: number[],
  ca: number[],
}

const dict = reactive({
  BDUsers: [] as any[],
  PMUsers: [] as any[],
  CAUsers: [] as any[],
  jobStatus: []
})

const status = reactive({
  initDict: false,
  loading: false,
})

const props = defineProps({ jobRequirementId: Number })
const jobRequirementId = toRef(props, 'jobRequirementId')
const emit = defineEmits(['close', 'update'])

const form = reactive({
  status: 0,
  priority: 5,
  bd: [] as number[],
  pm: [] as number[],
  ca: [] as number[],
  canPublishToPlatform: true,
})

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

async function initDict() {
  status.initDict = true
  try {
    const dictPromises = [
      getJobStatus(),
      getCompanyOnboardUsers('ca'),
      getCompanyOnboardUsers('bd'),
      getCompanyOnboardUsers('pm'),
    ]
    const [
      dictJobStatus,
      dictCAUsers,
      dictBDUsers,
      dictPMUsers
    ] = await Promise.all(dictPromises)

    dict.jobStatus = dictJobStatus.data
    dict.BDUsers = dictBDUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.CAUsers = dictCAUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
    dict.PMUsers = dictPMUsers.data.map((item: any, index: number) => { return { value: item.id, label: item.realName } })
  } catch (err: any) {
    message.error(`初始化失败！${err.message}`)
  }
  status.initDict = false
}

async function fetchJobDetail(jobRequirementId: number | undefined) {
  if (!jobRequirementId) return
  status.loading = true
  try {
    const res = await getJobRequirementDetail(jobRequirementId)
    form.status = res.data.status
    form.priority = res.data.priority || 5
    form.canPublishToPlatform = res.data.canPublishToPlatform == 0 ? false : true
    
    res.data.properties.forEach((item: any) => {
      if (['bd', 'pm', 'ca'].includes(item.key)) {
        if (item.key === 'bd') {
          if (!dict.BDUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.BDUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.bd.push(Number(item.value))
        }
        if (item.key === 'pm') {
          if (!dict.PMUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.PMUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.pm.push(Number(item.value))
        }
        if (item.key === 'ca') {
          if (!dict.CAUsers.some((user:any)=> user.value === Number(item.value))) {
            dict.CAUsers.push({ value: Number(item.value), label: item.valueName})
          }
          form.ca.push(Number(item.value))
        }
      }
    })
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

async function updateJob() {
  status.loading = true
  try {
    const invertedIndex = [] as any[]

    Object.entries({
      bd: form.bd,
      pm: form.pm,
      ca: form.ca,
    }).forEach((item, index) => {
      const [key, value] = item
      if (Array.isArray(value)) {
        value.forEach(itemValue => {
          invertedIndex.push({ key, value: itemValue })
        })
      } else invertedIndex.push({ key, value })
    })

    const res = await updateJobRequirement(props.jobRequirementId!, {
      status: form.status,
      priority: form.priority,
      canPublishToPlatform: form.canPublishToPlatform ? 1 : 0,
      invertedIndex
    })

    emit('update')
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

onMounted(async () => {
  await initDict()
  fetchJobDetail(props.jobRequirementId)
})

</script>

<style lang="scss" scoped>
.job-update-component {
  padding-bottom: 56px;

  .job-update-form {
    padding: 12px 24px;
  }

  .job-update-action {
    position: absolute;
    bottom: 0;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    width: 100%;
    padding: 12px 24px;
    text-align: right;
    box-sizing: border-box;
  }
}
</style>