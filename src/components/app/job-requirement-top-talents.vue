<template lang="pug">
.job-progress-component
  .job-progress
    a-spin(:spinning="status.loading")
      .talent-search(v-if="talentList.length")
        template(v-for="(item, index) in talentList")
          TalentGlobalSearchItem(
            v-if="item.site === 7"
            :item="item"
            :degree="dict.degreeMap"
            :reportContent="item.recommendDetail"
            @click="handleClickTalent(item)"
            @open="handleClickTalent"
          )
          TalentSearchItem(
            v-else
            :item="item"
            :degree="dict.degreeMap"
            :reportContent="item.recommendDetail"
            @click="handleClickTalent(item)"
            @open="handleClickTalent"
            @chat="() => handleChat(item)"
          )
        .job-progress__list-pagination
          a-pagination(
            v-model:current="currentPage"
            :total="total"
            :pageSize="pageSize"
            @change="handlePageChange"
          )
      .job-progress__empty(v-else)
        a-empty

  a-modal(v-model:open="status.showTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentDetailModal(:talentId="talentId" :jobId="jobRequirementId" :trackId="trackId" @close="status.showTalentDetail = false")

  a-modal(v-model:open="status.showGlobalTalentDetail" :destroyOnClose="true" :footer="false" width="90%" )
    TalentGlobalDetailModal(:talentId="globalTalentId" :jobId="jobRequirementId" :trackId="trackId" @close="status.showGlobalTalentDetail = false")

  a-drawer(v-model:open="status.showChatBox" :destroyOnClose="true" :width="480" title="聊天记录" :bodyStyle="{padding: 0}")
    ChatBox(:from="chatUsers.from" :to="chatUsers.to")
</template>

<script lang="ts" setup>
import { onMounted, toRef, reactive, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import JobProgressActions from '@/components/app/job-progress-actions.vue'
import TalentDetailModal from '@/components/app/talent-detail-modal.vue'
import TalentGlobalDetailModal from '@/components/app/talent-global-detail-modal.vue'
import TalentSearchItem from '@/components/app/talent-search-item.vue'
import TalentGlobalSearchItem from '@/components/app/talent-global-search-item.vue'
import ChatBox from '@/components/chat/chat-box.vue'
import { getTopTalentList } from '@/api/ai'
import { getDegreeList } from "@/api/dictionary"
import BossService from '@/service/multi/boss/index-v2'

const env = import.meta.env.VITE_VUE_APP_BUILD_ENV

const props = defineProps<{jobRequirementId:number}>()
const jobRequirementId = toRef(props, 'jobRequirementId')

const status = reactive({
  loading: false,
  showTalentDetail: false,
  showGlobalTalentDetail: false,
  showChatBox: false
})

const talentId = ref<number>()
const globalTalentId = ref<number>()
const trackId = ref('')
const route = useRoute()
const router = useRouter()

// Initialize currentPage from URL parameter
const currentPage = ref(Number(route.query.current) || 1)
const pageSize = ref(10)
const total = ref(0)
const talentList = ref([])
const dict = reactive({
  degreeMap: {} as any
})

const chatUsers = reactive({
  from: {}, to: {}
})

const FROM_USER = 'smart:0:smartdeer'

const bossService = new BossService()

const handleClickTalent = async (item: any) => {
  status.loading = true
  try {
    if (isNaN(Number(item.resumeUrl))) {
      await bossService.beforeOpen(item.bossLid, item.bossExpectId, item.bossSecurityId)
      if (env === 'production') {
        if (item.resumeUrl.indexOf('linkedin') !== -1) {
          window.open(item.resumeUrl + '?icbTrackId=' + item.trackId)
        } else {
          window.open(item.resumeUrl + '&icbTrackId=' + item.trackId)
        }
      } else {
        window.open(item.resumeUrl)
      }
    } else {
      if (Number(item.site) === 7) {
        status.showGlobalTalentDetail = true
        globalTalentId.value = item.id
      } else {
        talentId.value = Number(item.resumeUrl)
        trackId.value = item.trackId
        status.showTalentDetail = true
      }
    }
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const handleChat = (props: any) => {
  chatUsers.to = {
    imUserId: props.imUserId,
    nick: props.name,
    avatar: props.avatar
  }
  chatUsers.from = {
    imUserId: FROM_USER,
    nick: 'Jobs（乔布斯）',
    avatar: 'https://global-image.smartdeer.work/p/images/0x47b67a41f6324d2ea85c03270fe0064d.jpeg_median'
  }
  status.showChatBox = true
}

const fetchTalentList = async () => {
  status.loading = true
  try {
    const res = await getTopTalentList(jobRequirementId.value, {
      current: currentPage.value,
      size: pageSize.value
    })
    // Parse json field from records
    talentList.value = res.data.records.map((record: any) => {
      const talent = JSON.parse(record.json)
      // Merge the score information from the record
      if (record.scoreTotal) {
        talent.score = {
          final: record.scoreTotal,
          ...JSON.parse(record.scoreDetail)
        }
      }
      talent.recommendDetail = record.recommendReason
      return talent
    })
    total.value = res.data.total
  } catch (err: any) {
    message.error(err.message)
  }
  status.loading = false
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  // Update URL with current page
  router.push({
    query: {
      ...route.query,
      current: page
    }
  })
  fetchTalentList()
}

// 监听 jobId 变化
watch(jobRequirementId, (newVal) => {
  if (newVal) {
    // 重置分页和列表数据
    currentPage.value = 1
    total.value = 0
    talentList.value = []
    // 重新获取数据
    fetchTalentList()
  }
})

onMounted(async () => {
  // Initialize degree dictionary
  const res = await getDegreeList()
  dict.degreeMap = res.data
  // Fetch initial talent list
  await fetchTalentList()
})
</script>

<style lang="scss" scoped>
.job-progress-component {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.job-progress {
  flex: 1;
  margin-top: 12px;
  padding: 0 24px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 100%;

  &__list {
    flex: 1;
    overflow-y: auto;
    
    &-pagination {
      padding: 16px 16px;
      text-align: right;
      position: sticky;
      bottom: 0;
      background: white;
      z-index: 1;
    }
  }

  &__empty {
    padding: 20px 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.talent-search {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.job-progress-item {
  display: flex;
  flex: 1 1 auto;
  cursor: pointer;
  transition: all .2s;

  &:hover {
    opacity: .6;
    transition: all .2s;
  }

  &.active {
    color: #FF9111;

    .job-progress-item__index {
      background-color: #FF9111;
    }
  }

  &__index {
    border-radius: 50%;
    height: 16px;
    width: 16px;
    line-height: 16px;
    text-align: center;
    background-color: #8C8C8C;
    color: #fff;
    font-size: 12px;
    margin-right: 8px;
  }

  &__title {
    line-height: 16px;
    font-size: 14px;
  }
}

.resume-item {
  // height: 170px;
  padding: 24px;
  border-bottom: 1px solid #F7F8F8;
  position: relative;
  width: 100%;

  &__row {
    cursor: pointer;
    position: relative;
    padding-left: 70px;
    width: 100%;
  }

  .ant-avatar {
    position: absolute;
    left: 10px;
  }

  &__logo {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    background: #ccc;
  }

  &__title {
    margin-bottom: 10px;
    strong {
      font-size: 16px;
    }
    div {
      font-size: 14px;
      line-height: 20px;
    }
  }

  &__summary {
    display: flex;
  }

  &__content {
    flex-shrink: 0;
    width: 250px;

    div {
      // height: 20px;
      line-height: 20px;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__other {
    // display: flex;
    // margin-top: 24px;
    font-size: 14px;
    // display: flex;
    margin-left: 100px;
    flex: 1;
    padding-right: 130px;
    width: 0;

    .expirence-item {
      padding-left: 24px;
      padding-right: 24px;
      position: relative;
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: 0;
      }
      // display: flex;
      // overflow: hidden;

      &__icon {
        position: absolute;
        left: 0;
        top: 2px;

        img {
          width: 16px;
          display: block;
        }
      }

      &__exp {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &__time-range {
        width: 150px;
        padding-left: 8px;
      }
    }
  }

  &__btns {
    :deep(.ant-btn) {
      min-width: 128px;
      display: block;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}


.interview-list-container {
  padding: 16px;
  .drawer-action{
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    padding: 16px;
    text-align: right;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}

</style>