<template>
  <div>
    <div v-for="(item) in displayList" :key="String(item?.id)">
      <Item
        :key="String(item?.id)"
        :item="item"
        :degreeMap="degreeMap"
        :contact-status="getContactStatus(item)"
        :job-status="getJobStatus(item)"
        :score="item.score"
        :score-color="getScoreColor(item.score?.final)"
        :contact-color="getContactColor(item)"
        :job-color="getJobColor(item)"
        :unfit-options="unfitOptions"
        @chat="handleChat"
        @unfit="handleUnfit"
      />
    </div>
    <div class="pagination-container" v-if="total > 0">
      <a-pagination
        :current="currentPage"
        :total="total"
        :pageSize="pageSize"
        @change="handlePageChange"
        show-quick-jumper
        show-size-changer
        :pageSizeOptions="['10', '20', '50', '100']"
        @showSizeChange="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, PropType } from 'vue'
import Item from './item.vue'
import { getDegreeList } from '@/api/dictionary'
import { postRecommendStatus } from '@/api/notice'
interface TalentInfo {
  id: number
  name: string
  avatar: string
  score: { final: string, [key: string]: string }
  contactable?: boolean
  jobStatus?: string
  resumeUrl?: string
  parentId: number
  withContactWay?: string
}

const degreeMap = ref({})

onMounted(async () => {
  // console.log('jobRequirements', props.jobRequirements)
  const res = await getDegreeList()
  if (res?.code === 0) {
    degreeMap.value = res.data
  }
})

const props = defineProps({
  jobRequirements: {
    type: Array as PropType<TalentInfo[]>,
    required: true
  },
  degreeMap: {
    type: Object as PropType<Record<string, string>>,
    required: true
  },
  unfitOptions: {
    type: Array as PropType<string[]>,
    default: () => ['工作经验不符', '城市不符', '学历不符', '其他条件不符']
  },
  total: {
    type: Number,
    required: true
  },
  currentPage: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['pageChange', 'sizeChange', 'removeItem'])

const displayList = computed<TalentInfo[]>(() => props.jobRequirements)
// console.log(`displayList: `, displayList)
// debugger
const handlePageChange = (page: number) => {
  emit('pageChange', page)
}

const handleSizeChange = (current: number, size: number) => {
  emit('sizeChange', size)
}

const getScoreColor = (score: string | number | undefined): string => {
  const num = Number(score)
  if (num >= 80) return 'green'
  if (num >= 60) return 'gold'
  return 'red'
}
const getContactColor = (item: TalentInfo): string => item.contactable ? 'green' : 'gold'
const getJobColor = (item: TalentInfo): string => {
  if (item.jobStatus === '正在求职') return 'green'
  if (item.jobStatus === '目前在职') return 'gold'
  return 'red'
}
const getContactStatus = (item: TalentInfo): string => {
  // console.log('withContactWay', item.withContactWay);
  return item?.withContactWay === 'true' ? '可联系' : '无联系方式';
}

const getJobStatus = (item: TalentInfo): string => {
  // console.log(item);
  // console.log(item.jobStatus);
  // debugger
  return item.jobStatus || '未知'
}
const handleChat = (item: TalentInfo) => { 
  console.log('item', item)
  // debugger;
  // 跳转沟通页面
  if (item?.resumeUrl) {
    // debugger;
    window.open(`${item?.resumeUrl}`, '_blank')
  }

  // - contactStatus：是否已联系，0:未联系；1:已联系
  // - processStatus：流程状态，0:未处理；1:已推荐；2:不合适
  // - rejectReason：文本，不合适的原因
  postRecommendStatus({
    id: item.parentId,
    contactStatus: 1,
  })
}

const handleUnfit = (item: TalentInfo, reason: string) => {
  postRecommendStatus({
    id: item.parentId,
    contactStatus: 1,
    processStatus: 2,
    rejectReason: reason
  }).then(() => {
    emit('removeItem', item.parentId)
  })
}
</script>

<style scoped>
.list-header { display: flex; justify-content: space-between; align-items: center; }
.close-btn { color: #888; }
.more-btn { text-align: center; margin: 16px 0; }
.pagination-container {
  margin-top: 16px;
  text-align: right;
}
</style>